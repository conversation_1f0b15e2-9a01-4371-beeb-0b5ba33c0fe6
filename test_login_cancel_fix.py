#!/usr/bin/env python3
"""
测试登录取消修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_config_token_status():
    """检查配置文件中的令牌状态"""
    print("🔍 检查配置文件中的令牌状态...")
    
    try:
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        config_repo = container.get(JsonConfigRepository)
        saved_token = config_repo.get('API', 'default_token')
        
        print(f"🔑 配置文件中的令牌: {saved_token}")
        
        if saved_token:
            print("⚠️ 发现保存的令牌，应用会尝试自动登录")
            print("💡 要测试登录取消，需要清除令牌")
            return False
        else:
            print("✅ 没有保存的令牌，应该显示登录对话框")
            return True
            
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False

def test_modified_logic():
    """测试修改后的逻辑"""
    print("\n🔍 测试修改后的逻辑...")
    
    print("📋 新的启动流程:")
    print("1. 创建主窗口（但不显示）")
    print("2. 调用 try_auto_login()")
    print("3. 如果自动登录成功 → 显示主窗口")
    print("4. 如果自动登录失败 → 显示登录对话框")
    print("5. 如果用户点击'取消' → 直接退出，不显示主窗口")
    
    print("\n✅ 预期行为:")
    print("   - 没有令牌时显示登录对话框")
    print("   - 点击'取消'按钮 → 应用立即退出")
    print("   - 不会显示主窗口")
    
    return True

def main():
    """主函数"""
    print("🧪 登录取消修复测试")
    print("=" * 40)
    
    # 检查令牌状态
    no_token = test_config_token_status()
    
    # 测试修改后的逻辑
    test_modified_logic()
    
    print("\n📋 测试步骤:")
    print("1. 确保配置文件中没有保存的令牌")
    print("2. 启动应用: python run_gui.py")
    print("3. 应该显示登录对话框（不显示主窗口）")
    print("4. 点击'取消'按钮")
    print("5. 验证应用是否立即退出")
    
    if no_token:
        print("\n🚀 现在可以测试登录取消功能:")
        print("   python run_gui.py")
    else:
        print("\n⚠️ 需要先清除保存的令牌才能测试")

if __name__ == "__main__":
    main()
