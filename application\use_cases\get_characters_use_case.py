"""获取角色列表用例模块

此模块实现了获取角色列表的应用用例。
"""

from typing import List
from application.use_cases.base_use_case import UseCase
from application.dto.character_dto import CharacterDTO, GetCharactersRequest, GetCharactersResponse
from domain.repositories.character_repository import CharacterRepository


class GetCharactersUseCase(UseCase[GetCharactersRequest, GetCharactersResponse]):
    """获取角色列表用例"""
    
    def __init__(self, character_repository: CharacterRepository):
        """初始化用例
        
        Args:
            character_repository: 角色仓储
        """
        self._character_repository = character_repository
    
    def execute(self, request: GetCharactersRequest) -> GetCharactersResponse:
        """执行获取角色列表用例
        
        Args:
            request: 获取角色请求
            
        Returns:
            GetCharactersResponse: 角色列表响应
        """
        try:
            # 获取角色列表及其CV名字信息
            characters_with_cv_names = self._character_repository.get_characters_with_cv_names(request.book_id)

            # 转换为DTO
            character_dtos = [
                CharacterDTO(
                    id=char.id.to_string(),
                    name=char.name,
                    book_id=char.book_id,
                    cv_id=char.cv_id.to_string() if char.cv_id else None,
                    cv_name=cv_name,  # 添加CV名字
                    description=char.description,
                    is_cv_assigned=char.is_cv_assigned()
                )
                for char, cv_name in characters_with_cv_names
            ]
            
            return GetCharactersResponse(
                success=True,
                characters=character_dtos,
                total_count=len(character_dtos)
            )
            
        except Exception as e:
            return GetCharactersResponse(
                success=False,
                error_message=str(e),
                characters=[],
                total_count=0
            )
