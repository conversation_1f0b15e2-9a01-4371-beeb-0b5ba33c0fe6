#!/usr/bin/env python3
"""
配置文件和自动登录功能测试脚本

测试配置文件保存在项目目录和自动登录功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_config_location():
    """测试配置文件位置"""
    print("🔍 测试配置文件位置...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        config_path = config_manager.get_config_file_path()
        config_dir = Path(config_path).parent
        
        print(f"📁 配置目录: {config_dir}")
        print(f"📄 配置文件: {config_path}")
        
        # 检查是否在项目目录下
        project_root = Path(__file__).parent.absolute()
        is_in_project = str(config_dir).startswith(str(project_root))
        
        print(f"🏠 项目根目录: {project_root}")
        print(f"✅ 配置在项目目录: {'是' if is_in_project else '否'}")
        
        # 检查配置目录是否存在
        if config_dir.exists():
            print(f"📂 配置目录已存在")
            if Path(config_path).exists():
                print(f"📄 配置文件已存在")
            else:
                print(f"📄 配置文件不存在（首次运行正常）")
        else:
            print(f"📂 配置目录不存在（将自动创建）")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置位置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_operations():
    """测试配置操作"""
    print("\n🔍 测试配置操作...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # 测试加载配置
        print("📖 加载配置...")
        config = config_manager.load_config()
        print(f"✅ 配置加载成功")
        print(f"   API基础URL: {config.api_base_url}")
        print(f"   记住令牌: {config.remember_token}")
        print(f"   当前令牌: {'已设置' if config.api_token else '未设置'}")
        
        # 测试保存令牌
        print("\n💾 测试保存令牌...")
        test_token = "test_token_12345"
        success = config_manager.set_api_token(test_token, remember=True)
        print(f"✅ 令牌保存: {'成功' if success else '失败'}")
        
        # 测试读取令牌
        print("\n📖 测试读取令牌...")
        saved_token = config_manager.get_api_token()
        print(f"✅ 令牌读取: {'成功' if saved_token == test_token else '失败'}")
        print(f"   保存的令牌: {saved_token}")
        
        # 测试清除令牌
        print("\n🗑️ 测试清除令牌...")
        success = config_manager.clear_api_token()
        print(f"✅ 令牌清除: {'成功' if success else '失败'}")
        
        # 验证令牌已清除
        cleared_token = config_manager.get_api_token()
        print(f"✅ 令牌验证: {'已清除' if not cleared_token else '未清除'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_login_flow():
    """测试自动登录流程"""
    print("\n🔍 测试自动登录流程...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        print("📋 自动登录流程说明:")
        print("  1. 应用启动时调用 try_auto_login()")
        print("  2. 从配置文件读取保存的令牌")
        print("  3. 如果有令牌，验证其有效性")
        print("  4. 验证成功：自动登录，加载书籍")
        print("  5. 验证失败：清除无效令牌，显示登录对话框")
        print("  6. 无令牌：直接显示登录对话框")
        
        print("\n🔄 模拟自动登录流程:")
        
        # 场景1：无保存的令牌
        print("\n场景1: 无保存的令牌")
        config_manager.clear_api_token()
        saved_token = config_manager.get_api_token()
        if not saved_token:
            print("✅ 无令牌 → 应显示登录对话框")
        else:
            print("❌ 应该无令牌但检测到令牌")
        
        # 场景2：有保存的令牌（模拟）
        print("\n场景2: 有保存的令牌")
        test_token = "mock_valid_token"
        config_manager.set_api_token(test_token, remember=True)
        saved_token = config_manager.get_api_token()
        if saved_token == test_token:
            print("✅ 有令牌 → 应验证令牌有效性")
            print("   (在真实环境中会调用API验证)")
        else:
            print("❌ 令牌保存/读取失败")
        
        # 场景3：令牌验证失败（模拟）
        print("\n场景3: 令牌验证失败")
        print("✅ 验证失败 → 应清除无效令牌并显示登录对话框")
        config_manager.clear_api_token()
        
        return True
        
    except Exception as e:
        print(f"❌ 自动登录流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_structure():
    """测试配置文件结构"""
    print("\n🔍 测试配置文件结构...")
    
    try:
        from utils.config_manager import get_config_manager
        import json
        
        config_manager = get_config_manager()
        
        # 创建测试配置
        config_manager.set_api_token("test_token", remember=True)
        config_manager.set_api_base_url("https://test.api.com")
        config_manager.set_last_selected_book("book_123")
        
        # 读取配置文件内容
        config_path = config_manager.get_config_file_path()
        if Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            print("📄 配置文件内容:")
            print(json.dumps(config_data, indent=2, ensure_ascii=False))
            
            # 验证必要字段
            required_fields = ['api_token', 'api_base_url', 'remember_token']
            missing_fields = [field for field in required_fields if field not in config_data]
            
            if not missing_fields:
                print("✅ 配置文件结构正确")
            else:
                print(f"❌ 缺少字段: {missing_fields}")
        else:
            print("❌ 配置文件不存在")
            return False
        
        # 清理测试数据
        config_manager.clear_api_token()
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 配置文件和自动登录测试")
    print("=" * 50)
    
    # 测试配置文件位置
    location_success = test_config_location()
    
    # 测试配置操作
    operations_success = test_config_operations()
    
    # 测试自动登录流程
    auto_login_success = test_auto_login_flow()
    
    # 测试配置文件结构
    structure_success = test_config_file_structure()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 配置文件位置: {'✅ 通过' if location_success else '❌ 失败'}")
    print(f"  - 配置操作功能: {'✅ 通过' if operations_success else '❌ 失败'}")
    print(f"  - 自动登录流程: {'✅ 通过' if auto_login_success else '❌ 失败'}")
    print(f"  - 配置文件结构: {'✅ 通过' if structure_success else '❌ 失败'}")
    
    if all([location_success, operations_success, auto_login_success, structure_success]):
        print("\n🎉 所有测试通过！配置文件和自动登录功能正常。")
        print("\n📁 配置文件位置:")
        from utils.config_manager import get_config_manager
        config_path = get_config_manager().get_config_file_path()
        print(f"   {config_path}")
        
        print("\n🚀 启动GUI验证自动登录:")
        print("   python run_gui.py")
        print("\n💡 自动登录测试步骤:")
        print("   1. 首次启动会显示登录对话框")
        print("   2. 输入有效令牌并登录")
        print("   3. 关闭应用")
        print("   4. 再次启动应自动登录（无需输入令牌）")
        print("   5. 使用'重新登录'菜单可清除保存的令牌")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
