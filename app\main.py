#!/usr/bin/env python3
"""
CV分配工具主入口

这是独立分离架构的CV分配工具的主入口文件。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # 向上一级到项目根目录
sys.path.insert(0, str(project_root))

# 确保当前目录也在路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from app.container import container
    from application.character_application_service import CharacterApplicationService
except ImportError as e:
    print(f"导入错误: {e}")
    print(f"当前Python路径: {sys.path}")
    print(f"项目根目录: {project_root}")
    sys.exit(1)


def main():
    """主函数"""
    print("🚀 启动CV分配工具...")
    print("📋 独立分离架构版本")
    print("=" * 50)

    try:
        # 获取角色应用服务
        character_service = container.get(CharacterApplicationService)
        print("✅ 依赖注入容器初始化成功")

        # 启动GUI界面
        print("🎯 启动图形界面...")
        from presentation.gui.main_window import run_gui
        return run_gui()

    except ImportError as e:
        print(f"❌ GUI导入失败: {e}")
        print("💡 可能的原因:")
        print("   1. PyQt5未安装: pip install PyQt5")
        print("   2. 在无图形环境中运行")
        print("\n🔄 尝试启动控制台模式...")
        return run_console_mode()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1


def run_console_mode():
    """运行控制台模式"""
    print("📟 控制台模式")
    print("=" * 30)

    try:
        character_service = container.get(CharacterApplicationService)
        print("✅ 系统已就绪")
        print("💡 GUI功能请安装PyQt5: pip install PyQt5")

        # 简单的控制台交互
        while True:
            print("\n可用命令:")
            print("1. 测试连接")
            print("2. 退出")

            choice = input("\n请选择 (1-2): ").strip()

            if choice == "1":
                print("🔍 测试系统连接...")
                print("✅ 系统组件正常")
            elif choice == "2":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择")

        return 0
    except Exception as e:
        print(f"❌ 控制台模式启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
