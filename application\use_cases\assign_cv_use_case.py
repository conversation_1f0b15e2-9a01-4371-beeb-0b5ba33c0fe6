"""分配CV用例模块

此模块实现了分配CV给角色的应用用例。
"""

from application.use_cases.base_use_case import UseCase
from application.dto.assignment_dto import AssignCVRequest, AssignCVResponse
from domain.repositories.character_repository import CharacterRepository
from domain.repositories.cv_repository import CVRepository
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID
from domain.exceptions.character_exceptions import CharacterNotFoundError
from domain.exceptions.cv_exceptions import CVNotFoundError, CVNotAvailableError


class AssignCVUseCase(UseCase[AssignCVRequest, AssignCVResponse]):
    """分配CV用例"""
    
    def __init__(self, character_repository: CharacterRepository, cv_repository: CVRepository):
        """初始化用例
        
        Args:
            character_repository: 角色仓储
            cv_repository: CV仓储
        """
        self._character_repository = character_repository
        self._cv_repository = cv_repository
    
    def execute(self, request: AssignCVRequest) -> AssignCVResponse:
        """执行分配CV用例
        
        Args:
            request: 分配CV请求
            
        Returns:
            AssignCVResponse: 分配结果响应
        """
        try:
            character_id = CharacterID.from_string(request.character_id)
            cv_id = CVID.from_string(request.cv_id)
            
            # 验证角色存在
            character = self._character_repository.get_by_id(character_id)
            if not character:
                raise CharacterNotFoundError(f"角色不存在: {request.character_id}")
            
            # 验证CV存在且可用
            cv = self._cv_repository.get_by_id(cv_id)
            if not cv:
                raise CVNotFoundError(f"CV不存在: {request.cv_id}")
            
            if not cv.is_available:
                raise CVNotAvailableError(f"CV不可用: {cv.name}")
            
            # 执行分配
            character.assign_cv(cv_id)
            self._character_repository.save(character)
            
            return AssignCVResponse(
                success=True,
                message=f"成功将CV '{cv.name}' 分配给角色 '{character.name}'"
            )
            
        except Exception as e:
            return AssignCVResponse(
                success=False,
                error_message=str(e)
            )
