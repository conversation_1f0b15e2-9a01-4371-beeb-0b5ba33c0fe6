"""分配仓储接口模块

此模块定义了分配仓储的接口，用于管理分配记录的持久化操作。
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime
from domain.entities.assignment import Assignment, AssignmentStatus
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID


class AssignmentRepository(ABC):
    """分配仓储接口
    
    定义了分配记录数据访问的抽象接口，具体实现在基础设施层。
    """
    
    @abstractmethod
    def get_by_character_id(self, character_id: CharacterID) -> Optional[Assignment]:
        """根据角色ID获取分配记录
        
        Args:
            character_id: 角色ID
            
        Returns:
            Optional[Assignment]: 分配记录，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_by_book_id(self, book_id: str) -> List[Assignment]:
        """根据书籍ID获取所有分配记录
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 分配记录列表
        """
        pass
    
    @abstractmethod
    def get_by_status(self, status: AssignmentStatus, book_id: str) -> List[Assignment]:
        """根据状态获取分配记录
        
        Args:
            status: 分配状态
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 指定状态的分配记录列表
        """
        pass
    
    @abstractmethod
    def get_by_cv_id(self, cv_id: CVID, book_id: str) -> List[Assignment]:
        """根据CV ID获取分配记录
        
        Args:
            cv_id: CV ID
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 使用该CV的分配记录列表
        """
        pass
    
    @abstractmethod
    def get_successful_assignments(self, book_id: str) -> List[Assignment]:
        """获取成功的分配记录
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 成功的分配记录列表
        """
        pass
    
    @abstractmethod
    def get_failed_assignments(self, book_id: str) -> List[Assignment]:
        """获取失败的分配记录
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 失败的分配记录列表
        """
        pass
    
    @abstractmethod
    def get_pending_assignments(self, book_id: str) -> List[Assignment]:
        """获取待处理的分配记录
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 待处理的分配记录列表
        """
        pass
    
    @abstractmethod
    def save(self, assignment: Assignment) -> None:
        """保存分配记录
        
        Args:
            assignment: 要保存的分配记录
        """
        pass
    
    @abstractmethod
    def save_all(self, assignments: List[Assignment]) -> None:
        """批量保存分配记录
        
        Args:
            assignments: 要保存的分配记录列表
        """
        pass
    
    @abstractmethod
    def update_status(self, character_id: CharacterID, status: AssignmentStatus) -> bool:
        """更新分配状态
        
        Args:
            character_id: 角色ID
            status: 新状态
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    def delete_by_character_id(self, character_id: CharacterID) -> bool:
        """删除指定角色的分配记录
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 是否删除成功
        """
        pass
    
    @abstractmethod
    def exists(self, character_id: CharacterID) -> bool:
        """检查分配记录是否存在
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 是否存在
        """
        pass
    
    @abstractmethod
    def count_by_status(self, status: AssignmentStatus, book_id: str) -> int:
        """统计指定状态的分配记录数量
        
        Args:
            status: 分配状态
            book_id: 书籍ID
            
        Returns:
            int: 记录数量
        """
        pass
    
    @abstractmethod
    def get_assignments_by_date_range(self, start_date: datetime, 
                                    end_date: datetime, book_id: str) -> List[Assignment]:
        """根据日期范围获取分配记录
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            book_id: 书籍ID
            
        Returns:
            List[Assignment]: 指定日期范围内的分配记录列表
        """
        pass
