"""CV匹配器适配器模块

此模块提供了新领域服务与旧接口之间的适配器，确保向后兼容性。
"""

from typing import Dict, Tuple, Optional, List, Any
from domain.services.cv_matcher import CVMatcher
from domain.entities.cv import CV
from domain.value_objects.cv_id import CVID
from domain.value_objects.character_id import CharacterID
from domain.repositories.nickname_repository import NicknameRepository


class LegacyCVMatcherAdapter:
    """旧版CV匹配器适配器
    
    提供与旧版cv_matcher.py相同的接口，内部使用新的领域服务实现。
    这样可以确保现有代码无需修改即可使用新架构。
    """
    
    def __init__(self, nickname_repository: NicknameRepository):
        """初始化适配器
        
        Args:
            nickname_repository: 简名仓储
        """
        self._cv_matcher = CVMatcher(nickname_repository)
        self._nickname_repository = nickname_repository
    
    def get_cv_full_name(self, cv_nickname: str) -> str:
        """从CV简名对照表中查找CV全名
        
        兼容旧版本接口的方法。
        
        Args:
            cv_nickname: CV简名
            
        Returns:
            CV全名，如果找不到对应的全名，返回原名
        """
        return self._nickname_repository.get_full_name(cv_nickname.strip())
    
    def match_cv(self, cv_name: str, cv_map: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
        """匹配CV
        
        兼容旧版本接口的方法。
        
        Args:
            cv_name: 要匹配的CV名称
            cv_map: CV名称到ID的映射字典
            
        Returns:
            Tuple[Optional[str], Optional[str]]:
                - 第一个元素是匹配到的CV ID，如果未匹配到则为None
                - 第二个元素是匹配信息，如果是通过简名匹配则包含简名到全名的映射信息，否则为None
        """
        # 将字典转换为CV实体列表
        available_cvs = [
            CV(
                id=CVID.from_string(str(cv_id)),
                name=cv_name,
                book_id="unknown"  # 旧接口没有book_id信息
            )
            for cv_name, cv_id in cv_map.items()
        ]
        
        # 使用新的领域服务进行匹配
        result = self._cv_matcher.match_cv(cv_name, available_cvs)
        
        # 转换为旧版本的返回格式
        if result.is_successful():
            cv_id_str = result.cv_id.to_string()
            match_info = result.message if result.is_nickname_match() else None
            return cv_id_str, match_info
        else:
            match_info = result.message if result.message != "未找到匹配的CV" else None
            return None, match_info
    
    def batch_match_cvs(self, character_cv_map: Dict[str, str],
                        character_map: Dict[str, str],
                        cv_map: Dict[str, str]) -> Dict[str, Any]:
        """批量匹配角色和CV
        
        兼容旧版本接口的方法。
        
        Args:
            character_cv_map: 角色名称到CV名称的映射
            character_map: 角色名称到角色ID的映射
            cv_map: CV名称到CV ID的映射
            
        Returns:
            Dict[str, Any]: 包含以下键的结果字典：
                - 'success_matches': 成功匹配的列表，每项包含角色名、角色ID、CV名和CV ID
                - 'unmatched_characters': 未找到的角色列表
                - 'unmatched_cvs': 未找到的CV列表
                - 'cv_nickname_matches': 通过简名匹配成功的CV列表
        """
        results = {
            'success_matches': [],
            'unmatched_characters': [],
            'unmatched_cvs': [],
            'cv_nickname_matches': []
        }
        
        # 将字典转换为CV实体列表
        available_cvs = [
            CV(
                id=CVID.from_string(str(cv_id)),
                name=cv_name,
                book_id="unknown"
            )
            for cv_name, cv_id in cv_map.items()
        ]
        
        for character_name, cv_name in character_cv_map.items():
            # 检查角色是否存在
            if character_name not in character_map:
                results['unmatched_characters'].append(character_name)
                continue
            
            # 使用新的领域服务匹配CV
            match_result = self._cv_matcher.match_cv(cv_name, available_cvs)
            
            if match_result.is_successful():
                # 记录成功匹配
                results['success_matches'].append({
                    'character_name': character_name,
                    'character_id': str(character_map[character_name]),
                    'cv_name': cv_name,
                    'cv_id': match_result.cv_id.to_string()
                })
                
                # 如果是通过简名匹配，记录匹配信息
                if match_result.is_nickname_match():
                    results['cv_nickname_matches'].append(match_result.message)
            else:
                # 记录未匹配的CV
                if match_result.message != "未分配CV":
                    results['unmatched_cvs'].append(cv_name)
        
        return results


class SimpleCVMatcher:
    """简化的CV匹配器
    
    提供一个简化的接口，直接使用字典而不需要仓储。
    主要用于测试和简单场景。
    """
    
    def __init__(self, cv_nickname_map: Dict[str, str]):
        """初始化简化匹配器
        
        Args:
            cv_nickname_map: CV简名到全名的映射字典
        """
        self.cv_nickname_map = cv_nickname_map
    
    def get_cv_full_name(self, cv_nickname: str) -> str:
        """从CV简名对照表中查找CV全名"""
        nickname = cv_nickname.strip()
        return self.cv_nickname_map.get(nickname, cv_nickname)
    
    def match_cv(self, cv_name: str, cv_map: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
        """匹配CV - 简化版本"""
        # 处理特殊情况：CV名为"—"表示未分配CV
        if cv_name == "—":
            return None, "未分配CV"
        
        # 步骤1: 直接匹配CV名称
        if cv_name in cv_map:
            return str(cv_map[cv_name]), None
        
        # 步骤2: 通过简名对照表查找全名
        cv_full_name = self.get_cv_full_name(cv_name)
        
        # 步骤3: 检查全名是否在CV列表中
        if cv_full_name != cv_name and cv_full_name in cv_map:
            return str(cv_map[cv_full_name]), f"{cv_name} -> {cv_full_name}"
        
        return None, None
    
    def batch_match_cvs(self, character_cv_map: Dict[str, str],
                        character_map: Dict[str, str],
                        cv_map: Dict[str, str]) -> Dict[str, Any]:
        """批量匹配角色和CV - 简化版本"""
        results = {
            'success_matches': [],
            'unmatched_characters': [],
            'unmatched_cvs': [],
            'cv_nickname_matches': []
        }
        
        for character_name, cv_name in character_cv_map.items():
            # 检查角色是否存在
            if character_name not in character_map:
                results['unmatched_characters'].append(character_name)
                continue
            
            # 匹配CV
            cv_id, match_info = self.match_cv(cv_name, cv_map)
            
            if cv_id is None:
                results['unmatched_cvs'].append(cv_name)
                continue
            
            # 记录成功匹配
            results['success_matches'].append({
                'character_name': character_name,
                'character_id': str(character_map[character_name]),
                'cv_name': cv_name,
                'cv_id': cv_id
            })
            
            # 如果是通过简名匹配，记录匹配信息
            if match_info and match_info != "未分配CV":
                results['cv_nickname_matches'].append(match_info)
        
        return results
