"""CV匹配领域服务模块

此模块包含CV匹配的核心业务逻辑，遵循领域驱动设计原则。
"""

from typing import List, Dict, Optional
from domain.entities.cv import CV
from domain.value_objects.cv_id import CVID
from domain.value_objects.match_result import MatchResult, MatchType
from domain.repositories.nickname_repository import NicknameRepository


class CVMatcher:
    """CV匹配领域服务
    
    负责处理CV匹配的核心业务逻辑，包括直接匹配和简名匹配。
    这是一个纯领域服务，不依赖任何外部基础设施。
    
    Attributes:
        _nickname_repository: 简名仓储接口
    """
    
    def __init__(self, nickname_repository: NicknameRepository):
        """初始化CV匹配器
        
        Args:
            nickname_repository: 简名仓储接口，用于获取简名映射
        """
        self._nickname_repository = nickname_repository
    
    def match_cv(self, cv_name: str, available_cvs: List[CV]) -> MatchResult:
        """匹配CV
        
        这是核心的CV匹配业务逻辑，支持多种匹配策略：
        1. 处理特殊情况（未分配CV）
        2. 直接名称匹配
        3. 简名匹配
        
        Args:
            cv_name: 要匹配的CV名称
            available_cvs: 可用的CV列表
            
        Returns:
            MatchResult: 匹配结果值对象
        """
        if not cv_name:
            return MatchResult.no_match("CV名称为空")
        
        cv_name = cv_name.strip()
        
        # 处理特殊情况：CV名为"—"表示未分配CV
        if cv_name == "—":
            return MatchResult.unassigned()
        
        # 步骤1: 直接匹配CV名称
        direct_match = self._find_cv_by_name(cv_name, available_cvs)
        if direct_match:
            return MatchResult.direct_match(direct_match.id)
        
        # 步骤2: 通过简名匹配
        full_name = self._resolve_nickname(cv_name)
        if full_name != cv_name:
            nickname_match = self._find_cv_by_name(full_name, available_cvs)
            if nickname_match:
                return MatchResult.nickname_match(
                    nickname_match.id, cv_name, full_name
                )
        
        # 步骤3: 检查CV是否有匹配的简名
        for cv in available_cvs:
            if cv.matches_name_or_nickname(cv_name):
                return MatchResult.nickname_match(
                    cv.id, cv_name, cv.name
                )
        
        return MatchResult.no_match(f"未找到匹配的CV: {cv_name}")
    
    def batch_match_cvs(self, cv_names: List[str], available_cvs: List[CV]) -> List[MatchResult]:
        """批量匹配CV
        
        Args:
            cv_names: 要匹配的CV名称列表
            available_cvs: 可用的CV列表
            
        Returns:
            List[MatchResult]: 匹配结果列表
        """
        results = []
        for cv_name in cv_names:
            result = self.match_cv(cv_name, available_cvs)
            results.append(result)
        return results
    
    def find_best_matches(self, cv_name: str, available_cvs: List[CV], 
                         max_results: int = 5) -> List[MatchResult]:
        """查找最佳匹配
        
        返回多个可能的匹配结果，按置信度排序。
        
        Args:
            cv_name: 要匹配的CV名称
            available_cvs: 可用的CV列表
            max_results: 最大返回结果数
            
        Returns:
            List[MatchResult]: 按置信度排序的匹配结果列表
        """
        if not cv_name or cv_name.strip() == "—":
            return [MatchResult.unassigned()]
        
        cv_name = cv_name.strip()
        candidates = []
        
        # 直接匹配（最高优先级）
        for cv in available_cvs:
            if cv.name == cv_name:
                candidates.append(MatchResult.direct_match(cv.id))
        
        # 简名匹配
        full_name = self._resolve_nickname(cv_name)
        if full_name != cv_name:
            for cv in available_cvs:
                if cv.name == full_name:
                    candidates.append(MatchResult.nickname_match(
                        cv.id, cv_name, full_name, confidence=0.9
                    ))
        
        # CV自身的简名匹配
        for cv in available_cvs:
            if cv.has_nickname(cv_name):
                candidates.append(MatchResult.nickname_match(
                    cv.id, cv_name, cv.name, confidence=0.8
                ))
        
        # 模糊匹配（部分匹配）
        for cv in available_cvs:
            if cv_name in cv.name or cv.name in cv_name:
                candidates.append(MatchResult.success(
                    cv.id, MatchType.DIRECT, 
                    f"部分匹配: {cv_name} ≈ {cv.name}", 
                    confidence=0.6
                ))
        
        # 按置信度排序并去重
        candidates.sort(key=lambda x: x.confidence, reverse=True)
        unique_candidates = []
        seen_cv_ids = set()
        
        for candidate in candidates:
            if candidate.cv_id not in seen_cv_ids:
                unique_candidates.append(candidate)
                seen_cv_ids.add(candidate.cv_id)
                if len(unique_candidates) >= max_results:
                    break
        
        return unique_candidates if unique_candidates else [MatchResult.no_match(f"未找到匹配的CV: {cv_name}")]
    
    def get_match_statistics(self, cv_names: List[str], available_cvs: List[CV]) -> Dict[str, int]:
        """获取匹配统计信息
        
        Args:
            cv_names: 要匹配的CV名称列表
            available_cvs: 可用的CV列表
            
        Returns:
            Dict[str, int]: 统计信息字典
        """
        results = self.batch_match_cvs(cv_names, available_cvs)
        
        stats = {
            'total': len(results),
            'direct_matches': 0,
            'nickname_matches': 0,
            'no_matches': 0,
            'unassigned': 0
        }
        
        for result in results:
            if result.is_direct_match():
                stats['direct_matches'] += 1
            elif result.is_nickname_match():
                stats['nickname_matches'] += 1
            elif result.match_type == MatchType.NONE:
                if result.message == "未分配CV":
                    stats['unassigned'] += 1
                else:
                    stats['no_matches'] += 1
        
        return stats
    
    def _find_cv_by_name(self, name: str, available_cvs: List[CV]) -> Optional[CV]:
        """根据名称查找CV
        
        Args:
            name: CV名称
            available_cvs: 可用的CV列表
            
        Returns:
            Optional[CV]: 找到的CV，如果未找到则返回None
        """
        for cv in available_cvs:
            if cv.name == name:
                return cv
        return None
    
    def _resolve_nickname(self, nickname: str) -> str:
        """解析简名为全名
        
        Args:
            nickname: 简名
            
        Returns:
            str: 对应的全名，如果没有找到则返回原名
        """
        try:
            return self._nickname_repository.get_full_name(nickname)
        except Exception:
            # 如果仓储访问失败，返回原名
            return nickname
    
    def validate_cv_availability(self, cv: CV) -> bool:
        """验证CV是否可用
        
        Args:
            cv: 要验证的CV
            
        Returns:
            bool: 是否可用
        """
        return cv.is_available
    
    def get_matching_strategies(self) -> List[str]:
        """获取支持的匹配策略列表
        
        Returns:
            List[str]: 匹配策略名称列表
        """
        return [
            "direct_match",      # 直接匹配
            "nickname_match",    # 简名匹配
            "cv_nickname_match", # CV自身简名匹配
            "fuzzy_match"        # 模糊匹配
        ]
