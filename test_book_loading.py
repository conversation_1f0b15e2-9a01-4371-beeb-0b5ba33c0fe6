#!/usr/bin/env python3
"""
书籍加载功能测试脚本

测试书籍和角色数据的加载功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_book_service():
    """测试书籍服务"""
    try:
        print("🔍 测试书籍服务...")
        from app.container import container
        from application.book_application_service import BookApplicationService
        
        # 获取书籍服务
        book_service = container.get(BookApplicationService)
        print("✅ 书籍服务获取成功")
        
        # 测试获取书籍列表
        print("📚 获取书籍列表...")
        response = book_service.get_books()
        
        if response.success:
            print(f"✅ 成功获取 {len(response.books)} 本书籍:")
            for book in response.books:
                print(f"  - {book.name} (ID: {book.id}, 状态: {'已完成' if book.finished else '进行中'})")
        else:
            print(f"❌ 获取书籍失败: {response.error_message}")
        
        return response.success
        
    except Exception as e:
        print(f"❌ 书籍服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_service():
    """测试角色服务"""
    try:
        print("\n🔍 测试角色服务...")
        from app.container import container
        from application.character_application_service import CharacterApplicationService
        
        # 获取角色服务
        character_service = container.get(CharacterApplicationService)
        print("✅ 角色服务获取成功")
        
        # 测试获取角色列表（使用测试书籍ID）
        print("👥 获取角色列表...")
        response = character_service.get_characters("1")  # 使用模拟书籍ID
        
        if response.success:
            print(f"✅ 成功获取 {len(response.characters)} 个角色:")
            for character in response.characters:
                cv_status = f"CV: {character.cv_id}" if character.cv_id else "未分配CV"
                print(f"  - {character.name} (ID: {character.id}, {cv_status})")
        else:
            print(f"❌ 获取角色失败: {response.error_message}")
        
        return response.success
        
    except Exception as e:
        print(f"❌ 角色服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_client():
    """测试API客户端"""
    try:
        print("\n🔍 测试API客户端...")
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        # 创建API客户端
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        print("✅ API客户端创建成功")
        
        # 测试获取书籍
        print("📚 测试获取书籍...")
        success, books = client.get_books()
        
        if success:
            print(f"✅ API调用成功，获取 {len(books)} 本书籍")
        else:
            print("❌ API调用失败")
        
        # 测试获取角色
        print("👥 测试获取角色...")
        success, characters = client.get_characters("1")
        
        if success:
            print(f"✅ API调用成功，获取 {len(characters)} 个角色")
        else:
            print("❌ API调用失败")
        
        return True
        
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 书籍加载功能测试")
    print("=" * 50)
    
    # 测试API客户端
    api_success = test_api_client()
    
    # 测试书籍服务
    book_success = test_book_service()
    
    # 测试角色服务
    character_success = test_character_service()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - API客户端: {'✅ 通过' if api_success else '❌ 失败'}")
    print(f"  - 书籍服务: {'✅ 通过' if book_success else '❌ 失败'}")
    print(f"  - 角色服务: {'✅ 通过' if character_success else '❌ 失败'}")
    
    if all([api_success, book_success, character_success]):
        print("\n🎉 所有测试通过！GUI应用应该能正常工作。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
