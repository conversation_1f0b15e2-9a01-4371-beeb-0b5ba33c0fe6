#!/usr/bin/env python3
"""
拼音筛选功能测试脚本

测试角色和CV的拼音首字母筛选功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_pinyin_helper():
    """测试拼音助手功能"""
    print("🔍 测试拼音助手功能...")
    
    from utils.pinyin_helper import PinyinHelper
    
    # 测试拼音首字母提取
    test_names = [
        "张小雨", "李明轩", "王诗涵", "陈浩然", "刘雅琪",
        "赵子轩", "孙美琳", "周志强", "林若仙", "萧逸风",
        "温暖妈妈", "和蔼爸爸", "主角", "女主角", "反派",
        "配角A", "配角B", "兼职账号", "山竹超超好吃"
    ]
    
    print("\n📋 拼音首字母提取测试:")
    print("=" * 50)
    print(f"{'姓名':<15} {'拼音首字母'}")
    print("-" * 50)
    
    letter_groups = {}
    for name in test_names:
        letter = PinyinHelper.get_first_letter(name)
        print(f"{name:<15} {letter or '无法识别'}")
        
        if letter:
            if letter not in letter_groups:
                letter_groups[letter] = []
            letter_groups[letter].append(name)
    
    print("=" * 50)
    
    # 显示按字母分组的结果
    print("\n📊 按拼音首字母分组:")
    for letter in sorted(letter_groups.keys()):
        names = letter_groups[letter]
        print(f"  {letter}: {', '.join(names)} ({len(names)}个)")
    
    return True

def test_character_filtering():
    """测试角色筛选功能"""
    print("\n🔍 测试角色筛选功能...")
    
    try:
        from utils.pinyin_helper import PinyinHelper
        from application.dto.character_dto import CharacterDTO
        
        # 创建测试角色数据
        test_characters = [
            CharacterDTO("1", "张小雨", "1", "cv_001", "张小雨"),
            CharacterDTO("2", "李明轩", "1", "cv_002", "李明轩"),
            CharacterDTO("3", "王诗涵", "1", None, None),
            CharacterDTO("4", "陈浩然", "1", "cv_004", "陈浩然"),
            CharacterDTO("5", "刘雅琪", "1", None, None),
            CharacterDTO("6", "赵子轩", "1", "cv_006", "赵子轩"),
            CharacterDTO("7", "主角", "1", None, None),
            CharacterDTO("8", "女主角", "1", "cv_001", "张小雨"),
        ]
        
        print(f"✅ 创建了 {len(test_characters)} 个测试角色")
        
        # 测试不同字母的筛选
        test_letters = ["全部", "Z", "L", "W", "C", "X"]
        
        print("\n📋 角色筛选测试结果:")
        print("=" * 60)
        
        for letter in test_letters:
            if letter == "全部":
                filtered = test_characters
            else:
                filtered = PinyinHelper.filter_by_letter(
                    test_characters,
                    letter,
                    key_func=lambda char: char.name
                )
            
            print(f"\n筛选字母: {letter}")
            print(f"结果数量: {len(filtered)}")
            if filtered:
                names = [char.name for char in filtered]
                print(f"角色列表: {', '.join(names)}")
            else:
                print("无匹配角色")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ 角色筛选测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cv_filtering():
    """测试CV筛选功能"""
    print("\n🔍 测试CV筛选功能...")
    
    try:
        from utils.pinyin_helper import PinyinHelper
        
        # 创建测试CV数据（模拟不同格式）
        test_cvs = [
            {"id": "cv_001", "name": "张小雨"},
            {"id": "cv_002", "name": "李明轩"},
            {"id": "cv_003", "name": "王诗涵"},
            {"cvId": 379, "cvName": "兼职账号2"},
            {"cvId": 12, "cvName": "兼职账号"},
            {"cvId": 16, "cvName": "山竹超超好吃"},
            {"id": "cv_007", "name": "林若仙"},
            {"id": "cv_008", "name": "萧逸风"},
        ]
        
        print(f"✅ 创建了 {len(test_cvs)} 个测试CV")
        
        # 测试不同字母的筛选
        test_letters = ["全部", "Z", "L", "W", "J", "S", "X"]
        
        print("\n📋 CV筛选测试结果:")
        print("=" * 60)
        
        for letter in test_letters:
            if letter == "全部":
                filtered = test_cvs
            else:
                filtered = PinyinHelper.filter_by_letter(
                    test_cvs,
                    letter,
                    key_func=lambda cv: cv.get('name') or cv.get('cvName') or str(cv.get('id', ''))
                )
            
            print(f"\n筛选字母: {letter}")
            print(f"结果数量: {len(filtered)}")
            if filtered:
                names = []
                for cv in filtered:
                    name = cv.get('name') or cv.get('cvName') or str(cv.get('id', ''))
                    names.append(name)
                print(f"CV列表: {', '.join(names)}")
            else:
                print("无匹配CV")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ CV筛选测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔍 测试GUI集成...")
    
    print("📋 GUI筛选功能说明:")
    print("=" * 50)
    
    print("🎯 角色筛选器:")
    print("  - 位置: 角色列表上方")
    print("  - 功能: 按角色名称拼音首字母筛选")
    print("  - 按钮: 全部 + A-Z (27个按钮)")
    print("  - 样式: 可选中按钮，选中时高亮显示")
    print("  - 反馈: 状态栏显示筛选结果统计")
    
    print("\n🎯 CV筛选器:")
    print("  - 位置: CV选择区域上方")
    print("  - 功能: 按CV名字拼音首字母筛选")
    print("  - 按钮: 全部 + A-Z (27个按钮)")
    print("  - 样式: 与角色筛选器保持一致")
    print("  - 实时: 筛选后立即更新CV下拉框")
    
    print("\n🎨 用户体验:")
    print("  - 点击字母按钮立即筛选")
    print("  - 筛选结果实时更新")
    print("  - 无匹配时显示提示信息")
    print("  - 保持现有功能不变")
    
    print("\n🚀 使用场景:")
    print("  - 角色很多时快速定位")
    print("  - CV列表很长时快速筛选")
    print("  - 按姓氏首字母查找")
    print("  - 提升操作效率")
    
    return True

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 拼音筛选功能测试")
    print("=" * 50)
    
    # 测试拼音助手
    pinyin_success = test_pinyin_helper()
    
    # 测试角色筛选
    character_success = test_character_filtering()
    
    # 测试CV筛选
    cv_success = test_cv_filtering()
    
    # 测试GUI集成
    gui_success = test_gui_integration()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 拼音助手功能: {'✅ 通过' if pinyin_success else '❌ 失败'}")
    print(f"  - 角色筛选功能: {'✅ 通过' if character_success else '❌ 失败'}")
    print(f"  - CV筛选功能: {'✅ 通过' if cv_success else '❌ 失败'}")
    print(f"  - GUI集成说明: {'✅ 完成' if gui_success else '❌ 失败'}")
    
    if all([pinyin_success, character_success, cv_success, gui_success]):
        print("\n🎉 所有测试通过！拼音筛选功能正常。")
        print("\n🚀 启动GUI验证:")
        print("   python run_gui.py")
        print("\n💡 测试步骤:")
        print("   1. 选择'使用模拟数据'")
        print("   2. 选择任意书籍并加载角色")
        print("   3. 测试角色列表上方的拼音筛选按钮")
        print("   4. 测试CV选择区域的拼音筛选按钮")
        print("   5. 验证筛选结果的准确性")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
