"""基础用例接口模块

此模块定义了所有用例的基础接口。
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic

TRequest = TypeVar('TRequest')
TResponse = TypeVar('TResponse')


class UseCase(ABC, Generic[TRequest, TResponse]):
    """用例基础接口"""
    
    @abstractmethod
    def execute(self, request: TRequest) -> TResponse:
        """执行用例
        
        Args:
            request: 请求对象
            
        Returns:
            TResponse: 响应对象
        """
        pass
