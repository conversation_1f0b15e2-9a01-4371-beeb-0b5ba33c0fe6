"""角色实体单元测试"""

import pytest
from domain.entities.character import Character
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID
from domain.exceptions.character_exceptions import InvalidCharacterError


class TestCharacter:
    """角色实体测试类"""
    
    def test_create_character(self):
        """测试创建角色"""
        character_id = CharacterID.from_string("123")
        character = Character(
            id=character_id,
            name="主角",
            book_id="book_001"
        )
        
        assert character.id == character_id
        assert character.name == "主角"
        assert character.book_id == "book_001"
        assert character.cv_id is None
    
    def test_assign_cv(self):
        """测试分配CV"""
        character = Character(
            id=CharacterID.from_string("123"),
            name="主角",
            book_id="book_001"
        )
        
        cv_id = CVID.from_string("cv_001")
        character.assign_cv(cv_id)
        
        assert character.cv_id == cv_id
        assert character.is_cv_assigned()
    
    def test_invalid_character_name(self):
        """测试无效角色名称"""
        with pytest.raises(InvalidCharacterError):
            Character(
                id=CharacterID.from_string("123"),
                name="",
                book_id="book_001"
            )
    
    def test_character_equality(self):
        """测试角色相等性"""
        character_id = CharacterID.from_string("123")
        character1 = Character(id=character_id, name="主角", book_id="book_001")
        character2 = Character(id=character_id, name="配角", book_id="book_001")
        
        assert character1 == character2  # 基于ID比较
