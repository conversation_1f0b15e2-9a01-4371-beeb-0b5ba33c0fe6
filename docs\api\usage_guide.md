# API 使用指南

## 快速开始

### 1. 初始化容器

```python
from app.container import container
from application.character_application_service import CharacterApplicationService

# 获取角色应用服务
character_service = container.get(CharacterApplicationService)
```

### 2. 获取角色列表

```python
response = character_service.get_characters("book_001")
if response.success:
    for character in response.characters:
        print(f"角色: {character.name}, CV: {character.cv_id or '未分配'}")
```

### 3. 分配CV

```python
response = character_service.assign_cv("char_001", "cv_001", "book_001")
if response.success:
    print(response.message)
else:
    print(f"分配失败: {response.error_message}")
```

## 主要服务

### CharacterApplicationService

角色应用服务，提供角色相关的所有操作。

#### 方法

- `get_characters(book_id, include_assigned=True, include_unassigned=True)`: 获取角色列表
- `assign_cv(character_id, cv_id, book_id)`: 分配CV给角色
- `get_unassigned_characters(book_id)`: 获取未分配CV的角色
- `get_assigned_characters(book_id)`: 获取已分配CV的角色

## 错误处理

所有服务方法都返回包含 `success` 字段的响应对象：

```python
response = service.some_method()
if response.success:
    # 处理成功情况
    process_data(response.data)
else:
    # 处理错误情况
    handle_error(response.error_message)
```
