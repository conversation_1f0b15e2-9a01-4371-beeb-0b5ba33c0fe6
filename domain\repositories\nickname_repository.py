"""简名仓储接口模块

此模块定义了简名仓储的接口，用于管理CV的简名映射。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional


class NicknameRepository(ABC):
    """简名仓储接口
    
    定义了简名管理的抽象接口，具体实现在基础设施层。
    """
    
    @abstractmethod
    def get_full_name(self, nickname: str) -> str:
        """根据简名获取全名
        
        Args:
            nickname: 简名
            
        Returns:
            str: 对应的全名，如果没有找到则返回原简名
        """
        pass
    
    @abstractmethod
    def get_nickname_map(self) -> Dict[str, str]:
        """获取完整的简名映射
        
        Returns:
            Dict[str, str]: 简名到全名的映射字典
        """
        pass
    
    @abstractmethod
    def add_nickname(self, nickname: str, full_name: str) -> None:
        """添加简名映射
        
        Args:
            nickname: 简名
            full_name: 全名
        """
        pass
    
    @abstractmethod
    def remove_nickname(self, nickname: str) -> None:
        """移除简名映射
        
        Args:
            nickname: 要移除的简名
        """
        pass
    
    @abstractmethod
    def update_nickname(self, nickname: str, full_name: str) -> None:
        """更新简名映射
        
        Args:
            nickname: 简名
            full_name: 新的全名
        """
        pass
    
    @abstractmethod
    def has_nickname(self, nickname: str) -> bool:
        """检查是否存在指定的简名
        
        Args:
            nickname: 要检查的简名
            
        Returns:
            bool: 是否存在
        """
        pass
    
    @abstractmethod
    def get_nicknames_for_full_name(self, full_name: str) -> List[str]:
        """获取指定全名的所有简名
        
        Args:
            full_name: 全名
            
        Returns:
            List[str]: 简名列表
        """
        pass
    
    @abstractmethod
    def save(self) -> None:
        """保存简名映射到持久化存储"""
        pass
    
    @abstractmethod
    def reload(self) -> None:
        """从持久化存储重新加载简名映射"""
        pass
