"""GStudios API客户端模块

此模块包含与GStudios API交互的客户端实现，属于基础设施层。
"""

import requests
from typing import Dict, List, Tuple, Optional, Any
from urllib3.exceptions import InsecureRequestWarning

# 禁用 SSL 警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class APIConfig:
    """API配置类"""
    
    def __init__(self, base_url: str, token: Optional[str] = None):
        """初始化API配置
        
        Args:
            base_url: API基础URL
            token: 认证令牌
        """
        self.base_url = self._normalize_base_url(base_url)
        self.token = token
    
    def _normalize_base_url(self, base_url: str) -> str:
        """规范化基础URL"""
        if not base_url.startswith('http'):
            base_url = f"https://{base_url}"
        
        if base_url.endswith('/'):
            base_url = base_url[:-1]
        
        return base_url


class APIEndpoints:
    """API端点常量"""
    
    PARTNER_LIST = '/content/book/partner/list'
    INVITE_PARTNER = '/content/book/relation/freelancer/invite'
    CANCEL_INVITE = '/content/book/relation/freelancer/invite/cancel'
    GET_INVITED_PARTNERS = '/content/book/relation/freelancer/by/book'
    BOOK_LIST = '/content/book/list/editor'
    SEND_INVITATION = '/content/book/partner/invite'
    CHARACTER_LIST = '/content/character/list/book'
    CV_LIST_HUMAN = '/record/cv/list/human'
    CV_LIST_ROBOT = '/record/cv/list/robot'
    CHARACTER_UPDATE = '/content/character/update/info'


class GStudiosAPIClient:
    """GStudios API客户端
    
    负责与GStudios API进行HTTP通信的基础设施组件。
    """
    
    def __init__(self, config: APIConfig):
        """初始化API客户端
        
        Args:
            config: API配置对象
        """
        self._config = config
        self._session = requests.Session()
        self._setup_headers()
    
    def _setup_headers(self):
        """设置HTTP请求头"""
        self._session.headers.update({
            "x-requested-with": "XMLHttpRequest",
            "accept": "application/json, text/plain, */*"
        })
        
        if self._config.token:
            self._session.headers.update({
                "authorization": f"Bearer {self._config.token}"
            })
    
    def set_token(self, token: str) -> None:
        """设置认证令牌
        
        Args:
            token: 认证令牌
        """
        self._config.token = token
        self._session.headers.update({
            "authorization": f"Bearer {token}"
        })
    
    def get_books(self, finished: str = "all") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
                - "all": 获取所有书籍
                - "finished": 获取已完成的书籍
                - "unfinished": 获取未完成的书籍
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 书籍列表)
        """
        url = f"{self._config.base_url}{APIEndpoints.BOOK_LIST}"
        
        finished_param = ""
        if finished == "finished":
            finished_param = "true"
        elif finished == "unfinished":
            finished_param = "false"
        
        params = {
            "pageNo": 1,
            "pageSize": 50,
            "name": "",
            "remark": "",
            "finished": finished_param,
            "sortItem": "",
            "sortAsc": ""
        }
        
        try:
            # 检查是否有认证令牌
            if not self._config.token:
                # 返回一些模拟数据用于测试
                mock_books = [
                    {"id": "1", "name": "测试书籍1", "finished": False, "remark": "这是一个测试书籍"},
                    {"id": "2", "name": "测试书籍2", "finished": True, "remark": "这是另一个测试书籍"},
                    {"id": "3", "name": "示例小说", "finished": False, "remark": "示例小说描述"}
                ]
                return True, mock_books

            response = self._session.get(url, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                return False, []

            books = data.get('data', {}).get('list', [])
            books.sort(key=lambda x: x.get('name', ''))
            return True, books

        except Exception as e:
            print(f"获取书籍列表时发生异常: {str(e)}")
            return False, []
    
    def get_characters(self, book_id: str, **filters) -> Tuple[bool, List[Dict[str, Any]]]:
        """获取角色列表
        
        Args:
            book_id: 书籍ID
            **filters: 其他筛选参数
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 角色列表)
        """
        url = f"{self._config.base_url}{APIEndpoints.CHARACTER_LIST}"
        
        params = {
            "bookId": book_id,
            "roleType": "book"
        }
        
        # 添加筛选参数
        for key, value in filters.items():
            if value:
                params[key] = value
        
        try:
            # 检查是否有认证令牌
            if not self._config.token:
                # 返回一些模拟角色数据用于测试
                mock_characters = [
                    {"id": "1", "name": "主角", "cvHumanId": None, "cvHumanName": None, "description": "故事的主人公"},
                    {"id": "2", "name": "女主角", "cvHumanId": "cv_001", "cvHumanName": "张小雨", "description": "女性主角"},
                    {"id": "3", "name": "反派", "cvHumanId": None, "cvHumanName": None, "description": "故事的反派角色"},
                    {"id": "4", "name": "配角A", "cvHumanId": "cv_002", "cvHumanName": "李明轩", "description": "重要配角"},
                    {"id": "5", "name": "配角B", "cvHumanId": None, "cvHumanName": None, "description": "次要配角"}
                ]
                return True, mock_characters

            response = self._session.get(url, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                return False, []

            return True, data.get('data', [])

        except Exception as e:
            print(f"获取角色列表时发生异常: {str(e)}")
            return False, []
    
    def get_cvs(self, book_id: str, cv_type: str = "human") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取CV列表
        
        Args:
            book_id: 书籍ID
            cv_type: CV类型 ("human" 或 "robot")
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, CV列表)
        """
        endpoint = APIEndpoints.CV_LIST_HUMAN if cv_type == "human" else APIEndpoints.CV_LIST_ROBOT
        url = f"{self._config.base_url}{endpoint}"
        params = {"bookId": book_id}
        
        try:
            # 检查是否有认证令牌
            if not self._config.token:
                # 根据不同书籍返回不同的CV列表（模拟真实情况）
                book_cvs = {
                    "1": [  # 测试书籍1 - 现代都市小说
                        {"id": "cv_001", "name": "张小雨", "gender": "女", "age": "青年", "description": "温柔甜美的女声"},
                        {"id": "cv_002", "name": "李明轩", "gender": "男", "age": "青年", "description": "阳光帅气的男声"},
                        {"id": "cv_003", "name": "王诗涵", "gender": "女", "age": "少女", "description": "清纯可爱的少女音"},
                        {"id": "cv_004", "name": "陈浩然", "gender": "男", "age": "中年", "description": "成熟稳重的男声"}
                    ],
                    "2": [  # 测试书籍2 - 古风仙侠小说
                        {"id": "cv_005", "name": "刘雅琪", "gender": "女", "age": "中年", "description": "知性优雅的女声"},
                        {"id": "cv_006", "name": "赵子轩", "gender": "男", "age": "少年", "description": "活泼开朗的少年音"},
                        {"id": "cv_009", "name": "林若仙", "gender": "女", "age": "青年", "description": "空灵仙气的女声"},
                        {"id": "cv_010", "name": "萧逸风", "gender": "男", "age": "青年", "description": "潇洒不羁的男声"}
                    ],
                    "3": [  # 示例小说 - 家庭温情小说
                        {"id": "cv_007", "name": "孙美琳", "gender": "女", "age": "老年", "description": "慈祥温和的老年女声"},
                        {"id": "cv_008", "name": "周志强", "gender": "男", "age": "老年", "description": "威严厚重的老年男声"},
                        {"id": "cv_011", "name": "温暖妈妈", "gender": "女", "age": "中年", "description": "温暖慈爱的母亲声音"},
                        {"id": "cv_012", "name": "和蔼爸爸", "gender": "男", "age": "中年", "description": "和蔼可亲的父亲声音"}
                    ]
                }

                # 返回对应书籍的CV列表，如果书籍不存在则返回默认列表
                mock_cvs = book_cvs.get(book_id, book_cvs["1"])
                return True, mock_cvs

            response = self._session.get(url, params=params, verify=False)
            response.raise_for_status()
            data = response.json()

            if data.get('code') != 1:
                return False, []

            cv_list = data.get('data', {}).get('list', [])
            return True, cv_list

        except Exception as e:
            print(f"获取CV列表时发生异常: {str(e)}")
            return False, []
    
    def assign_cv_to_character(self, character_id: str, cv_id: str) -> Tuple[bool, str]:
        """分配CV给角色
        
        Args:
            character_id: 角色ID
            cv_id: CV ID
        
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        url = f"{self._config.base_url}{APIEndpoints.CHARACTER_UPDATE}"
        data = {
            "id": character_id,
            "cvHumanId": cv_id,
            "cvHumanTask": True,
            "autoMixMode": 0,
        }
        
        try:
            response = self._session.post(url, json=data, verify=False)
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') == 1:
                return True, "分配CV成功"
            else:
                return False, result.get('msg', '未知错误')
                
        except Exception as e:
            return False, str(e)
    
    def get_partners(self, book_id: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """获取合作伙伴列表
        
        Args:
            book_id: 书籍ID
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 合作伙伴列表)
        """
        url = f"{self._config.base_url}{APIEndpoints.PARTNER_LIST}"
        params = {
            "bookId": book_id,
            "subjectType": "1"
        }
        
        try:
            response = self._session.get(url, params=params, verify=False)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') != 1:
                return False, []
            
            return True, data.get('data', {}).get('list', [])
            
        except Exception as e:
            print(f"获取合作伙伴列表时发生异常: {str(e)}")
            return False, []
    
    def invite_partner(self, book_id: str, subject_id: str, price: float) -> Tuple[bool, str]:
        """邀请合作伙伴
        
        Args:
            book_id: 书籍ID
            subject_id: 合作伙伴ID
            price: 价格
        
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        url = f"{self._config.base_url}{APIEndpoints.INVITE_PARTNER}"
        data = {
            "bookId": book_id,
            "subjectId": subject_id,
            "price": price
        }
        
        try:
            response = self._session.post(url, json=data, verify=False)
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') == 1:
                return True, "邀请成功"
            else:
                return False, result.get('msg', '邀请失败')
                
        except Exception as e:
            return False, str(e)
