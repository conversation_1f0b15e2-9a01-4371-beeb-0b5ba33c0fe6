#!/usr/bin/env python3
"""
简单的令牌验证测试

测试验证功能是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_api_client_directly():
    """直接测试API客户端"""
    print("🔍 直接测试API客户端...")
    
    try:
        from app.container import container
        from infrastructure.api.gstudios_api_service import GStudiosAPIService
        
        # 获取API服务
        api_service = container.get(GStudiosAPIService)
        print("✅ API服务获取成功")
        
        # 测试无效令牌
        invalid_token = "invalid_test_token"
        print(f"🔧 设置无效令牌: {invalid_token}")
        api_service.set_token(invalid_token)
        
        # 直接调用API客户端
        print(f"📡 调用API客户端...")
        try:
            success, books = api_service._client.get_books("all")
            print(f"📊 结果: success={success}, books_count={len(books) if books else 0}")
            
            if success:
                print(f"⚠️ 无效令牌返回成功，这可能是模拟数据")
                if books:
                    print(f"📋 返回的书籍: {[book.get('name', 'Unknown') for book in books[:3]]}")
            else:
                print(f"✅ 无效令牌正确返回失败")
                
        except Exception as e:
            print(f"📊 API调用异常: {e}")
            if "401" in str(e):
                print(f"✅ 检测到401错误，令牌验证正确")
            else:
                print(f"⚠️ 其他错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_dialog_creation():
    """测试登录对话框创建"""
    print("\n🔍 测试登录对话框创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from presentation.gui.dialogs.login_dialog import LoginDialog
        
        # 创建应用实例
        app = QApplication([])
        print("✅ QApplication创建成功")
        
        # 创建登录对话框
        dialog = LoginDialog()
        print("✅ 登录对话框创建成功")
        
        # 清理
        app.quit()
        print("✅ 应用清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 简单令牌验证测试")
    print("=" * 40)
    
    # 测试API客户端
    api_success = test_api_client_directly()
    
    # 测试登录对话框创建
    dialog_success = test_login_dialog_creation()
    
    # 总结
    print("\n📊 测试结果:")
    print(f"  - API客户端测试: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"  - 登录对话框测试: {'✅ 成功' if dialog_success else '❌ 失败'}")
    
    if api_success and dialog_success:
        print("\n💡 基础组件工作正常")
        print("🚀 可以启动GUI进行手动测试:")
        print("   python run_gui.py")
        print("\n📋 手动测试步骤:")
        print("   1. 输入无效令牌（如'test123'）")
        print("   2. 点击'登录'按钮")
        print("   3. 观察验证过程")
        print("   4. 检查是否显示错误提示")
        return 0
    else:
        print("\n⚠️ 基础组件有问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
