"""分配数据传输对象模块

此模块定义了分配相关的数据传输对象。
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class AssignCVRequest:
    """分配CV请求"""
    character_id: str
    cv_id: str
    book_id: str


@dataclass
class AssignCVResponse:
    """分配CV响应"""
    success: bool
    message: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class BatchAssignRequest:
    """批量分配请求"""
    book_id: str
    assignments: dict  # character_name -> cv_name 映射


@dataclass
class BatchAssignResponse:
    """批量分配响应"""
    success: bool
    total_processed: int
    successful_assignments: int
    failed_assignments: int
    details: list
    error_message: Optional[str] = None
