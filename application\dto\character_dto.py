"""角色数据传输对象模块

此模块定义了角色相关的数据传输对象。
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class CharacterDTO:
    """角色数据传输对象"""
    id: str
    name: str
    book_id: str
    cv_id: Optional[str] = None
    cv_name: Optional[str] = None  # CV的显示名称
    description: Optional[str] = None
    is_cv_assigned: bool = False


@dataclass
class GetCharactersRequest:
    """获取角色列表请求"""
    book_id: str
    include_assigned: bool = True
    include_unassigned: bool = True


@dataclass
class GetCharactersResponse:
    """获取角色列表响应"""
    success: bool
    characters: List[CharacterDTO]
    total_count: int
    error_message: Optional[str] = None
