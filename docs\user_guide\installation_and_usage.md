# 用户使用指南

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 所需依赖包（见 requirements.txt）

### 2. 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd project-directory

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 运行设置脚本
python scripts/setup_dev_environment.py
```

### 3. 配置文件

复制 `.env.example` 为 `.env` 并填入配置：

```
GSTUDIOS_API_BASE_URL=https://www.gstudios.com.cn/story_v2/api
GSTUDIOS_API_TOKEN=your_token_here
```

## 使用说明

### 1. 启动应用

```bash
python main.py
```

### 2. 基本操作

1. **选择书籍**: 从下拉列表中选择要操作的书籍
2. **查看角色**: 系统会自动加载角色列表
3. **分配CV**: 选择角色和CV，点击分配按钮
4. **批量操作**: 使用Excel导入功能进行批量分配

### 3. 高级功能

- **简名匹配**: 系统支持CV简名自动匹配
- **冲突检测**: 自动检测和处理分配冲突
- **历史记录**: 查看分配历史和变更记录

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API令牌是否正确
   - 确认API基础URL设置

2. **角色加载失败**
   - 检查书籍ID是否正确
   - 验证用户权限

3. **CV分配失败**
   - 确认CV是否可用
   - 检查角色是否存在

### 日志查看

应用日志保存在 `logs/` 目录下，按日期分类。
