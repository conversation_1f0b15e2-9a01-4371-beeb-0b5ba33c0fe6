#!/usr/bin/env python3
"""
CV分配工具测试启动脚本

这个脚本用于测试基本的导入和初始化功能。
"""

import sys
import os
from pathlib import Path

def main():
    """主测试函数"""
    print("🚀 启动CV分配工具测试...")
    print("📋 独立分离架构版本")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 确保项目根目录在Python路径中
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    print(f"📁 项目根目录: {project_root}")
    print(f"🐍 Python路径: {sys.path[0]}")
    
    try:
        # 测试基本导入
        print("\n🔍 测试基本导入...")
        
        # 测试领域层导入
        print("  - 测试领域层...")
        from domain.value_objects.character_id import CharacterID
        from domain.value_objects.cv_id import CVID
        print("    ✅ 值对象导入成功")
        
        # 测试应用层导入
        print("  - 测试应用层...")
        from application.dto.character_dto import CharacterDTO
        print("    ✅ DTO导入成功")
        
        # 测试基础设施层导入
        print("  - 测试基础设施层...")
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        print("    ✅ 配置仓储导入成功")
        
        print("\n✅ 基本导入测试通过")
        
        # 测试简单的对象创建
        print("\n🔧 测试对象创建...")
        character_id = CharacterID.from_string("test_123")
        cv_id = CVID.from_string("cv_456")
        config_repo = JsonConfigRepository()
        
        print(f"  - 角色ID: {character_id}")
        print(f"  - CV ID: {cv_id}")
        print(f"  - 配置仓储: {type(config_repo).__name__}")
        
        print("\n✅ 对象创建测试通过")
        print("\n🎯 基本功能测试完成，系统核心组件正常")
        
        return 0
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print(f"📁 当前工作目录: {os.getcwd()}")
        print("\n💡 可能的解决方案:")
        print("   1. 检查文件是否存在")
        print("   2. 检查相对导入是否正确")
        print("   3. 确保所有 __init__.py 文件存在")
        return 1
    except Exception as e:
        print(f"\n❌ 其他错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
