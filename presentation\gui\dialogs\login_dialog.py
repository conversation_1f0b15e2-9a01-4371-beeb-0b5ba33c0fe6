"""登录对话框模块

此模块实现了API认证的登录对话框。
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QCheckBox, QMessageBox, QGroupBox, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from utils.token_validator import validate_api_token


class LoginDialog(QDialog):
    """登录对话框"""
    
    # 登录成功信号
    login_successful = pyqtSignal(str)  # 传递token
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.token = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("CV分配工具 - API认证")
        self.setFixedSize(450, 350)
        self.setModal(True)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🔐 GStudios API 认证")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 说明文本
        info_group = QGroupBox("使用说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(80)
        info_text.setPlainText(
            "请输入您的GStudios API认证令牌以访问真实数据。\n"
            "如果您没有令牌，可以选择使用模拟数据进行测试。"
        )
        info_text.setStyleSheet("border: none; background: transparent;")
        info_layout.addWidget(info_text)
        main_layout.addWidget(info_group)
        
        # 令牌输入区域
        token_group = QGroupBox("API令牌")
        token_layout = QVBoxLayout(token_group)
        
        # 令牌输入框
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("请输入您的API令牌...")
        self.token_input.setEchoMode(QLineEdit.Password)
        token_layout.addWidget(self.token_input)
        
        # 显示/隐藏密码
        self.show_password_cb = QCheckBox("显示令牌")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        token_layout.addWidget(self.show_password_cb)
        
        main_layout.addWidget(token_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 使用模拟数据按钮
        self.demo_btn = QPushButton("使用模拟数据")
        self.demo_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.demo_btn.clicked.connect(self.use_demo_data)
        button_layout.addWidget(self.demo_btn)
        
        button_layout.addStretch()
        
        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.login_btn.clicked.connect(self.login)
        button_layout.addWidget(self.login_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接回车键到登录
        self.token_input.returnPressed.connect(self.login)
        
        # 设置焦点
        self.token_input.setFocus()
    
    def toggle_password_visibility(self, checked):
        """切换密码可见性"""
        if checked:
            self.token_input.setEchoMode(QLineEdit.Normal)
        else:
            self.token_input.setEchoMode(QLineEdit.Password)
    
    def login(self):
        """登录处理"""
        token = self.token_input.text().strip()

        if not token:
            QMessageBox.warning(self, "警告", "请输入API令牌")
            return

        # 简单验证令牌格式（可以根据实际需要调整）
        if len(token) < 10:
            QMessageBox.warning(self, "警告", "API令牌格式不正确，长度过短")
            return

        # 验证令牌有效性
        print(f"🔍 开始验证令牌: {token[:8]}...")

        # 使用try-finally确保进度窗口被关闭
        verification_success = False
        try:
            verification_success = self.verify_token(token)
        except Exception as verify_error:
            print(f"⚠️ 验证过程中发生异常: {verify_error}")
            verification_success = False

        if verification_success:
            print(f"✅ 令牌验证成功")
            self.token = token
            self.login_successful.emit(token)
            self.accept()
        else:
            print(f"❌ 令牌验证失败")
            # 确保所有进度窗口都已关闭
            self._ensure_progress_dialogs_closed()

            QMessageBox.critical(
                self,
                "登录失败",
                "API令牌验证失败！\n\n请检查：\n"
                "1. 令牌是否正确\n"
                "2. 网络连接是否正常\n"
                "3. API服务是否可用\n\n"
                "如果您想测试界面功能，\n"
                "请点击'使用模拟数据'按钮。"
            )
    
    def use_demo_data(self):
        """使用模拟数据"""
        reply = QMessageBox.question(
            self, 
            "确认", 
            "您确定要使用模拟数据吗？\n\n"
            "模拟数据仅用于测试界面功能，\n"
            "无法访问真实的书籍和角色数据。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.token = None  # 使用None表示模拟模式
            self.login_successful.emit("")  # 空字符串表示模拟模式
            self.accept()
    
    def verify_token(self, token: str) -> bool:
        """验证API令牌是否有效

        Args:
            token: 要验证的令牌

        Returns:
            令牌是否有效
        """
        progress = None
        try:
            print(f"🔍 开始API令牌验证...")
            from app.container import container
            from infrastructure.api.gstudios_api_service import GStudiosAPIService
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QTimer

            # 显示验证进度
            progress = QMessageBox(self)
            progress.setWindowTitle("验证中")
            progress.setText("正在验证API令牌，请稍候...")
            progress.setStandardButtons(QMessageBox.NoButton)
            progress.show()

            # 处理事件以显示进度对话框
            QApplication.processEvents()

            print(f"📡 获取API服务...")
            # 获取API服务
            api_service = container.get(GStudiosAPIService)

            # 临时设置令牌进行验证
            print(f"🔧 设置临时令牌...")
            original_token = getattr(api_service, '_current_token', None)
            api_service.set_token(token)

            print(f"📚 直接调用API客户端验证令牌...")

            # 设置验证结果变量
            verification_result = False
            api_error_occurred = False

            try:
                # 直接调用API客户端
                success, books = api_service._client.get_books("all")
                print(f"📊 API客户端响应: success={success}")
                print(f"📊 书籍数量: {len(books) if books else 0}")

                # 验证结果：必须成功且有数据（或者是空的有效响应）
                verification_result = success and books is not None
                print(f"🎯 API调用成功，验证结果: {verification_result}")

            except Exception as api_error:
                print(f"📊 API调用异常: {api_error}")
                api_error_occurred = True

                # 如果是401错误，说明令牌无效
                if "401" in str(api_error) or "Unauthorized" in str(api_error):
                    print(f"🎯 检测到401错误，令牌无效")
                    verification_result = False
                else:
                    # 其他错误也认为验证失败
                    print(f"🎯 其他API错误，验证失败")
                    verification_result = False

            finally:
                # 确保恢复原始令牌
                try:
                    if original_token:
                        api_service.set_token(original_token)
                        print(f"🔄 已恢复原始令牌")
                except Exception as restore_error:
                    print(f"⚠️ 恢复原始令牌时出错: {restore_error}")

                # 确保关闭进度对话框
                try:
                    if progress:
                        progress.close()
                        print(f"✅ 进度对话框已关闭")
                except Exception as close_error:
                    print(f"⚠️ 关闭进度对话框时出错: {close_error}")

            print(f"🎯 最终验证结果: {verification_result}")
            return verification_result

        except Exception as e:
            print(f"⚠️ 令牌验证时发生错误: {e}")
            import traceback
            traceback.print_exc()

            # 确保关闭进度对话框
            try:
                if progress:
                    progress.close()
                    print(f"✅ 异常处理中关闭了进度对话框")
            except Exception as close_error:
                print(f"⚠️ 异常处理中关闭进度对话框时出错: {close_error}")

            return False

    def _ensure_progress_dialogs_closed(self):
        """确保所有进度对话框都被关闭"""
        try:
            from PyQt5.QtWidgets import QApplication

            # 处理所有待处理的事件
            QApplication.processEvents()

            # 查找并关闭所有QMessageBox类型的窗口
            for widget in QApplication.allWidgets():
                if isinstance(widget, QMessageBox):
                    if widget.windowTitle() == "验证中":
                        print(f"🔧 强制关闭验证进度窗口")
                        widget.close()
                        widget.deleteLater()

            # 再次处理事件确保窗口被清理
            QApplication.processEvents()

        except Exception as e:
            print(f"⚠️ 清理进度窗口时出错: {e}")

    def get_token(self):
        """获取输入的令牌"""
        return self.token


def show_login_dialog(parent=None):
    """显示登录对话框的便捷函数
    
    Returns:
        tuple: (success, token) - 成功标志和令牌
    """
    dialog = LoginDialog(parent)
    result = dialog.exec_()
    
    if result == QDialog.Accepted:
        return True, dialog.get_token()
    else:
        return False, None
