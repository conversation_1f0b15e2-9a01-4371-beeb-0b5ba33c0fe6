"""登录对话框模块

此模块实现了API认证的登录对话框。
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QCheckBox, QMessageBox, QGroupBox, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from utils.token_validator import validate_api_token


class LoginDialog(QDialog):
    """登录对话框"""
    
    # 登录成功信号
    login_successful = pyqtSignal(str)  # 传递token
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.token = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("CV分配工具 - API认证")
        self.setFixedSize(450, 350)
        self.setModal(True)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🔐 GStudios API 认证")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 说明文本
        info_group = QGroupBox("使用说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(80)
        info_text.setPlainText(
            "请输入您的GStudios API认证令牌以访问真实数据。\n"
            "如果您没有令牌，可以选择使用模拟数据进行测试。"
        )
        info_text.setStyleSheet("border: none; background: transparent;")
        info_layout.addWidget(info_text)
        main_layout.addWidget(info_group)
        
        # 令牌输入区域
        token_group = QGroupBox("API令牌")
        token_layout = QVBoxLayout(token_group)
        
        # 令牌输入框
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("请输入您的API令牌...")
        self.token_input.setEchoMode(QLineEdit.Password)
        token_layout.addWidget(self.token_input)
        
        # 显示/隐藏密码
        self.show_password_cb = QCheckBox("显示令牌")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        token_layout.addWidget(self.show_password_cb)
        
        main_layout.addWidget(token_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 使用模拟数据按钮
        self.demo_btn = QPushButton("使用模拟数据")
        self.demo_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.demo_btn.clicked.connect(self.use_demo_data)
        button_layout.addWidget(self.demo_btn)
        
        button_layout.addStretch()
        
        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.login_btn.clicked.connect(self.login)
        button_layout.addWidget(self.login_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接回车键到登录
        self.token_input.returnPressed.connect(self.login)
        
        # 设置焦点
        self.token_input.setFocus()
    
    def toggle_password_visibility(self, checked):
        """切换密码可见性"""
        if checked:
            self.token_input.setEchoMode(QLineEdit.Normal)
        else:
            self.token_input.setEchoMode(QLineEdit.Password)
    
    def login(self):
        """登录处理"""
        token = self.token_input.text().strip()
        
        if not token:
            QMessageBox.warning(self, "警告", "请输入API令牌")
            return
        
        # 简单验证令牌格式（可以根据实际需要调整）
        if len(token) < 10:
            QMessageBox.warning(self, "警告", "API令牌格式不正确，长度过短")
            return
        
        self.token = token
        self.login_successful.emit(token)
        self.accept()
    
    def use_demo_data(self):
        """使用模拟数据"""
        reply = QMessageBox.question(
            self, 
            "确认", 
            "您确定要使用模拟数据吗？\n\n"
            "模拟数据仅用于测试界面功能，\n"
            "无法访问真实的书籍和角色数据。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.token = None  # 使用None表示模拟模式
            self.login_successful.emit("")  # 空字符串表示模拟模式
            self.accept()
    
    def get_token(self):
        """获取输入的令牌"""
        return self.token


def show_login_dialog(parent=None):
    """显示登录对话框的便捷函数
    
    Returns:
        tuple: (success, token) - 成功标志和令牌
    """
    dialog = LoginDialog(parent)
    result = dialog.exec_()
    
    if result == QDialog.Accepted:
        return True, dialog.get_token()
    else:
        return False, None
