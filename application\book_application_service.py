"""书籍应用服务模块

此模块实现了书籍相关的应用服务，协调用例执行。
"""

from typing import List
from .use_cases.get_books_use_case import GetBooksUseCase
from .dto.book_dto import GetBooksRequest, GetBooksResponse, BookDTO


class BookApplicationService:
    """书籍应用服务"""
    
    def __init__(self, get_books_use_case: GetBooksUseCase):
        """初始化应用服务
        
        Args:
            get_books_use_case: 获取书籍用例
        """
        self._get_books_use_case = get_books_use_case
    
    def get_books(self, finished: str = "all") -> GetBooksResponse:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
                - "all": 获取所有书籍
                - "finished": 获取已完成的书籍
                - "unfinished": 获取未完成的书籍
            
        Returns:
            GetBooksResponse: 书籍列表响应
        """
        request = GetBooksRequest(finished=finished)
        return self._get_books_use_case.execute(request)
    
    def get_all_books(self) -> List[BookDTO]:
        """获取所有书籍的简化方法
        
        Returns:
            List[BookDTO]: 书籍列表
        """
        response = self.get_books("all")
        return response.books if response.success else []
    
    def get_finished_books(self) -> List[BookDTO]:
        """获取已完成的书籍
        
        Returns:
            List[BookDTO]: 已完成的书籍列表
        """
        response = self.get_books("finished")
        return response.books if response.success else []
    
    def get_unfinished_books(self) -> List[BookDTO]:
        """获取未完成的书籍
        
        Returns:
            List[BookDTO]: 未完成的书籍列表
        """
        response = self.get_books("unfinished")
        return response.books if response.success else []
