#!/usr/bin/env python3
"""
测试登录取消退出功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_login_dialog_standalone():
    """独立测试登录对话框"""
    print("🔍 独立测试登录对话框...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from presentation.gui.dialogs.login_dialog import show_login_dialog
        
        app = QApplication([])
        
        print("📋 模拟用户点击取消...")
        # 这里我们无法自动化点击，但可以验证函数逻辑
        
        # 手动测试说明
        print("💡 手动测试步骤:")
        print("   1. 运行: python run_gui.py")
        print("   2. 在登录对话框中点击'取消'按钮")
        print("   3. 验证应用是否立即退出")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_config_token():
    """检查配置文件中的令牌"""
    print("\n🔍 检查配置文件中的令牌...")
    
    try:
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        config_repo = container.get(JsonConfigRepository)
        saved_token = config_repo.get('API', 'default_token')
        
        print(f"🔑 配置文件中的令牌: {saved_token}")
        
        if saved_token:
            print("⚠️ 发现保存的令牌，应用会尝试自动登录")
            print("💡 要测试登录对话框，需要清除令牌:")
            print("   1. 删除配置文件中的default_token")
            print("   2. 或者设置为null")
            return False
        else:
            print("✅ 没有保存的令牌，应该显示登录对话框")
            return True
            
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False

def clear_saved_token():
    """清除保存的令牌以便测试"""
    print("\n🗑️ 清除保存的令牌...")
    
    try:
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        config_repo = container.get(JsonConfigRepository)
        config_repo.set('API', 'default_token', None)
        config_repo.save()
        
        print("✅ 令牌已清除")
        print("💡 现在启动应用应该显示登录对话框")
        return True
        
    except Exception as e:
        print(f"❌ 清除令牌失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 登录取消退出功能测试")
    print("=" * 40)
    
    # 检查配置文件中的令牌
    no_token = check_config_token()
    
    if not no_token:
        # 如果有令牌，清除它
        clear_saved_token()
    
    # 测试登录对话框
    test_login_dialog_standalone()
    
    print("\n📋 完整测试步骤:")
    print("1. 确保配置文件中没有保存的令牌")
    print("2. 启动应用: python run_gui.py")
    print("3. 应该显示登录对话框")
    print("4. 点击'取消'按钮")
    print("5. 验证应用是否立即退出")
    
    print("\n✅ 预期行为:")
    print("   - 点击'取消' → 应用立即关闭")
    print("   - 不应该进入主界面")
    print("   - 不应该显示空白窗口")

if __name__ == "__main__":
    main()
