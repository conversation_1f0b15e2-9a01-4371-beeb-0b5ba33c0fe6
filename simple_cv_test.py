#!/usr/bin/env python3
"""
简单的CV名字测试

直接测试API客户端和仓储的CV名字功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_direct_api():
    """直接测试API客户端"""
    print("🔍 直接测试API客户端...")
    
    from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
    
    # 创建API客户端（无令牌，使用模拟数据）
    config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
    client = GStudiosAPIClient(config)
    
    # 获取角色数据
    success, characters = client.get_characters("1")
    
    if success:
        print(f"✅ 获取到 {len(characters)} 个角色:")
        for char in characters:
            print(f"  角色: {char['name']}")
            print(f"  CV ID: {char.get('cvHumanId')}")
            print(f"  CV名字: {char.get('cvHumanName')}")
            print()
    else:
        print("❌ 获取角色失败")

def test_direct_repository():
    """直接测试角色仓储"""
    print("🔍 直接测试角色仓储...")

    from infrastructure.api.gstudios_api_service import GStudiosAPIService
    from infrastructure.repositories.gstudios_character_repository import GStudiosCharacterRepository

    # 创建API服务（无令牌，使用模拟数据）
    api_service = GStudiosAPIService("https://www.gstudios.com.cn/story_v2/api", None)

    # 创建角色仓储
    repo = GStudiosCharacterRepository(api_service)

    # 测试新方法
    characters_with_cv_names = repo.get_characters_with_cv_names("1")

    print(f"✅ 获取到 {len(characters_with_cv_names)} 个角色:")
    for character, cv_name in characters_with_cv_names:
        print(f"  角色: {character.name}")
        print(f"  CV ID: {character.cv_id}")
        print(f"  CV名字: {cv_name}")
        print()

def main():
    """主函数"""
    print("🧪 简单CV名字测试")
    print("=" * 30)
    
    test_direct_api()
    print()
    test_direct_repository()

if __name__ == "__main__":
    main()
