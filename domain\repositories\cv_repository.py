"""CV仓储接口模块

此模块定义了CV仓储的接口，用于管理CV数据的持久化操作。
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from ..entities.cv import CV
from ..value_objects.cv_id import CVID


class CVRepository(ABC):
    """CV仓储接口
    
    定义了CV数据访问的抽象接口，具体实现在基础设施层。
    """
    
    @abstractmethod
    def get_by_id(self, cv_id: CVID) -> Optional[CV]:
        """根据ID获取CV
        
        Args:
            cv_id: CV ID
            
        Returns:
            Optional[CV]: CV实体，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_by_book_id(self, book_id: str) -> List[CV]:
        """根据书籍ID获取CV列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CV]: CV列表
        """
        pass
    
    @abstractmethod
    def get_by_name(self, name: str, book_id: str) -> Optional[CV]:
        """根据名称和书籍ID获取CV
        
        Args:
            name: CV名称
            book_id: 书籍ID
            
        Returns:
            Optional[CV]: CV实体，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_available_cvs(self, book_id: str) -> List[CV]:
        """获取可用的CV列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CV]: 可用的CV列表
        """
        pass
    
    @abstractmethod
    def get_human_cvs(self, book_id: str) -> List[CV]:
        """获取人工CV列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CV]: 人工CV列表
        """
        pass
    
    @abstractmethod
    def get_robot_cvs(self, book_id: str) -> List[CV]:
        """获取AI CV列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CV]: AI CV列表
        """
        pass
    
    @abstractmethod
    def save(self, cv: CV) -> None:
        """保存CV
        
        Args:
            cv: 要保存的CV实体
        """
        pass
    
    @abstractmethod
    def save_all(self, cvs: List[CV]) -> None:
        """批量保存CV
        
        Args:
            cvs: 要保存的CV列表
        """
        pass
    
    @abstractmethod
    def update_availability(self, cv_id: CVID, is_available: bool) -> bool:
        """更新CV的可用性
        
        Args:
            cv_id: CV ID
            is_available: 是否可用
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    def exists(self, cv_id: CVID) -> bool:
        """检查CV是否存在
        
        Args:
            cv_id: CV ID
            
        Returns:
            bool: 是否存在
        """
        pass
    
    @abstractmethod
    def count_by_book_id(self, book_id: str) -> int:
        """统计指定书籍的CV数量
        
        Args:
            book_id: 书籍ID
            
        Returns:
            int: CV数量
        """
        pass
    
    @abstractmethod
    def find_by_nickname(self, nickname: str, book_id: str) -> List[CV]:
        """根据简名查找CV
        
        Args:
            nickname: 简名
            book_id: 书籍ID
            
        Returns:
            List[CV]: 匹配的CV列表
        """
        pass
    
    @abstractmethod
    def search_by_name_pattern(self, pattern: str, book_id: str) -> List[CV]:
        """根据名称模式搜索CV
        
        Args:
            pattern: 搜索模式
            book_id: 书籍ID
            
        Returns:
            List[CV]: 匹配的CV列表
        """
        pass
