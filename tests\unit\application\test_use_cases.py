"""应用用例单元测试"""

import pytest
from unittest.mock import Mock
from application.use_cases.get_characters_use_case import GetCharactersUseCase
from application.dto.character_dto import GetCharactersRequest
from domain.entities.character import Character
from domain.value_objects.character_id import CharacterID


class TestGetCharactersUseCase:
    """获取角色用例测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.character_repository = Mock()
        self.use_case = GetCharactersUseCase(self.character_repository)
    
    def test_get_characters_success(self):
        """测试成功获取角色列表"""
        # 准备测试数据
        characters = [
            Character(
                id=CharacterID.from_string("1"),
                name="主角",
                book_id="book_001"
            ),
            Character(
                id=CharacterID.from_string("2"),
                name="配角",
                book_id="book_001"
            )
        ]
        
        self.character_repository.get_by_book_id.return_value = characters
        
        # 执行用例
        request = GetCharactersRequest(book_id="book_001")
        response = self.use_case.execute(request)
        
        # 验证结果
        assert response.success is True
        assert len(response.characters) == 2
        assert response.characters[0].name == "主角"
        assert response.characters[1].name == "配角"
    
    def test_get_characters_failure(self):
        """测试获取角色列表失败"""
        # 模拟异常
        self.character_repository.get_by_book_id.side_effect = Exception("数据库错误")
        
        # 执行用例
        request = GetCharactersRequest(book_id="book_001")
        response = self.use_case.execute(request)
        
        # 验证结果
        assert response.success is False
        assert response.error_message == "数据库错误"
        assert len(response.characters) == 0
