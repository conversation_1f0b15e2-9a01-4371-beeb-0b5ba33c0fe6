#!/usr/bin/env python3
"""
CV加载功能测试脚本

测试书籍CV列表的动态加载功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_cv_api():
    """测试CV API功能"""
    try:
        print("🔍 测试CV API功能...")
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        # 创建API客户端（无令牌，使用模拟数据）
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        print("✅ API客户端创建成功")
        
        # 测试获取CV列表
        print("🎤 获取CV列表...")
        success, cvs = client.get_cvs("1", "human")
        
        if success:
            print(f"✅ 成功获取 {len(cvs)} 个CV:")
            print("\n📋 CV列表详情:")
            print("=" * 80)
            print(f"{'CV ID':<10} {'CV名字':<12} {'性别':<6} {'年龄':<8} {'描述'}")
            print("-" * 80)
            
            for cv in cvs:
                cv_id = cv.get('id', 'Unknown')
                name = cv.get('name', 'Unknown')
                gender = cv.get('gender', 'Unknown')
                age = cv.get('age', 'Unknown')
                description = cv.get('description', 'No description')
                
                print(f"{cv_id:<10} {name:<12} {gender:<6} {age:<8} {description}")
            
            print("=" * 80)
            return True
        else:
            print("❌ 获取CV列表失败")
            return False
        
    except Exception as e:
        print(f"❌ CV API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cv_service():
    """测试CV服务功能"""
    try:
        print("\n🔍 测试CV服务功能...")
        from app.container import container
        from infrastructure.api.gstudios_api_service import GStudiosAPIService
        
        # 获取API服务
        api_service = container.get(GStudiosAPIService)
        print("✅ API服务获取成功")
        
        # 测试获取CV列表
        print("🎤 通过服务获取CV列表...")
        success, cvs = api_service.get_cvs("1", "human")
        
        if success:
            print(f"✅ 服务调用成功，获取 {len(cvs)} 个CV")
            
            # 模拟GUI下拉框的显示逻辑
            print("\n📋 GUI下拉框预览:")
            print("┌─ 选择CV ─────────────────────┐")
            print("│ 请选择CV...                  │")
            for cv in cvs:
                cv_name = cv.get('name', cv.get('id', '未知CV'))
                print(f"│ {cv_name:<28} │")
            print("└─────────────────────────────┘")
            
            return True
        else:
            print("❌ 服务调用失败")
            return False
        
    except Exception as e:
        print(f"❌ CV服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cv_assignment_flow():
    """测试CV分配流程"""
    print("\n🔍 测试CV分配流程...")
    
    print("📋 完整的CV分配流程:")
    print("=" * 50)
    
    print("1. 用户启动应用并登录")
    print("2. 选择书籍分类（全部/进行中/已完成）")
    print("3. 从书籍列表中选择目标书籍")
    print("4. 点击'加载角色'按钮")
    print("   ├─ 后台加载角色列表")
    print("   └─ 后台加载该书籍的CV列表")
    print("5. 角色表格显示所有角色及其CV状态")
    print("6. CV下拉框显示该书籍所有可用CV")
    print("7. 用户选择角色，然后选择CV进行分配")
    
    print("\n🎯 CV分配面板改进:")
    print("  - CV下拉框动态加载当前书籍的CV")
    print("  - 显示CV的实际名字而不是ID")
    print("  - 支持多种CV类型（年龄、性别等）")
    print("  - 提供CV的详细描述信息")
    
    print("\n💡 用户体验提升:")
    print("  - 无需手动输入CV ID")
    print("  - 可以看到所有可用的CV选项")
    print("  - CV名字更直观易懂")
    print("  - 避免分配不存在的CV")
    
    return True

def test_mock_data_quality():
    """测试模拟数据质量"""
    print("\n🔍 测试模拟数据质量...")
    
    try:
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        
        success, cvs = client.get_cvs("1", "human")
        
        if success:
            print(f"📊 模拟数据统计:")
            print(f"  - 总CV数量: {len(cvs)}")
            
            # 按性别统计
            male_count = len([cv for cv in cvs if cv.get('gender') == '男'])
            female_count = len([cv for cv in cvs if cv.get('gender') == '女'])
            print(f"  - 男性CV: {male_count} 个")
            print(f"  - 女性CV: {female_count} 个")
            
            # 按年龄统计
            age_groups = {}
            for cv in cvs:
                age = cv.get('age', '未知')
                age_groups[age] = age_groups.get(age, 0) + 1
            
            print(f"  - 年龄分布:")
            for age, count in age_groups.items():
                print(f"    * {age}: {count} 个")
            
            print(f"\n✅ 模拟数据质量良好，覆盖多种CV类型")
            return True
        else:
            print("❌ 模拟数据获取失败")
            return False
        
    except Exception as e:
        print(f"❌ 模拟数据测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - CV加载功能测试")
    print("=" * 50)
    
    # 测试CV API
    api_success = test_cv_api()
    
    # 测试CV服务
    service_success = test_cv_service()
    
    # 测试CV分配流程
    flow_success = test_cv_assignment_flow()
    
    # 测试模拟数据质量
    data_success = test_mock_data_quality()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - CV API功能: {'✅ 通过' if api_success else '❌ 失败'}")
    print(f"  - CV服务功能: {'✅ 通过' if service_success else '❌ 失败'}")
    print(f"  - CV分配流程: {'✅ 完成' if flow_success else '❌ 失败'}")
    print(f"  - 模拟数据质量: {'✅ 良好' if data_success else '❌ 问题'}")
    
    if all([api_success, service_success, flow_success, data_success]):
        print("\n🎉 所有测试通过！CV加载功能正常。")
        print("\n🚀 启动GUI测试CV功能:")
        print("   python run_gui.py")
        print("\n💡 测试步骤:")
        print("   1. 选择'使用模拟数据'")
        print("   2. 选择任意书籍")
        print("   3. 点击'加载角色'")
        print("   4. 查看CV下拉框是否显示8个CV选项")
        print("   5. CV名字应该显示为：张小雨、李明轩等")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
