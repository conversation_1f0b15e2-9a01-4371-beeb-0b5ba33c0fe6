#!/usr/bin/env python3
"""
CV名字显示功能测试脚本

测试角色列表中CV名字的正确显示
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_character_with_cv_names():
    """测试角色及CV名字获取"""
    try:
        print("🔍 测试角色及CV名字获取...")
        from app.container import container
        from application.character_application_service import CharacterApplicationService
        
        # 获取角色服务
        character_service = container.get(CharacterApplicationService)
        print("✅ 角色服务获取成功")
        
        # 测试获取角色列表（使用测试书籍ID）
        print("👥 获取角色列表及CV名字...")
        response = character_service.get_characters("1")  # 使用模拟书籍ID
        
        if response.success:
            print(f"✅ 成功获取 {len(response.characters)} 个角色:")
            for character in response.characters:
                cv_display = "未分配"
                if character.cv_name:
                    cv_display = f"{character.cv_name} (ID: {character.cv_id})"
                elif character.cv_id:
                    cv_display = f"ID: {character.cv_id}"
                
                print(f"  - {character.name}")
                print(f"    角色ID: {character.id}")
                print(f"    CV信息: {cv_display}")
                print(f"    状态: {'已分配' if character.is_cv_assigned else '未分配'}")
                print()
        else:
            print(f"❌ 获取角色失败: {response.error_message}")
        
        return response.success
        
    except Exception as e:
        print(f"❌ 角色服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_repository():
    """测试角色仓储的CV名字功能"""
    try:
        print("🔍 测试角色仓储CV名字功能...")
        from app.container import container
        from domain.repositories.character_repository import CharacterRepository
        
        # 获取角色仓储
        character_repo = container.get(CharacterRepository)
        print("✅ 角色仓储获取成功")
        
        # 测试新的方法
        print("📊 测试 get_characters_with_cv_names 方法...")
        characters_with_cv_names = character_repo.get_characters_with_cv_names("1")
        
        print(f"✅ 获取到 {len(characters_with_cv_names)} 个角色及其CV信息:")
        for character, cv_name in characters_with_cv_names:
            print(f"  - 角色: {character.name}")
            print(f"    CV ID: {character.cv_id}")
            print(f"    CV名字: {cv_name or '无'}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 角色仓储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_client_cv_data():
    """测试API客户端的CV数据"""
    try:
        print("🔍 测试API客户端CV数据...")
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        # 创建API客户端（无令牌，使用模拟数据）
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        print("✅ API客户端创建成功")
        
        # 测试获取角色数据
        print("👥 测试获取角色数据...")
        success, characters = client.get_characters("1")
        
        if success:
            print(f"✅ API调用成功，获取 {len(characters)} 个角色:")
            for char in characters:
                print(f"  - 角色: {char.get('name', 'Unknown')}")
                print(f"    CV ID: {char.get('cvHumanId', 'None')}")
                print(f"    CV名字: {char.get('cvHumanName', 'None')}")
                print()
        else:
            print("❌ API调用失败")
        
        return success
        
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - CV名字显示功能测试")
    print("=" * 50)
    
    # 测试API客户端CV数据
    api_success = test_api_client_cv_data()
    
    # 测试角色仓储
    repo_success = test_character_repository()
    
    # 测试角色服务
    service_success = test_character_with_cv_names()
    
    # 总结
    print("📊 测试结果总结:")
    print(f"  - API客户端CV数据: {'✅ 通过' if api_success else '❌ 失败'}")
    print(f"  - 角色仓储CV名字: {'✅ 通过' if repo_success else '❌ 失败'}")
    print(f"  - 角色服务CV显示: {'✅ 通过' if service_success else '❌ 失败'}")
    
    if all([api_success, repo_success, service_success]):
        print("\n🎉 所有测试通过！GUI中应该能正确显示CV名字。")
        print("\n💡 预期效果:")
        print("  - 已分配CV的角色显示CV名字（如：张小雨、李明轩）")
        print("  - 未分配CV的角色显示'未分配'")
        print("  - 如果只有CV ID没有名字，则显示CV ID")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
