#!/usr/bin/env python3
"""
CV分配工具GUI启动脚本

专门用于启动图形界面版本的CV分配工具。
"""

import sys
import os
from pathlib import Path

def main():
    """主启动函数"""
    print("🚀 启动CV分配工具GUI...")
    print("📋 独立分离架构版本")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 确保项目根目录在Python路径中
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置工作目录
    os.chdir(project_root)
    
    try:
        # 测试依赖注入容器
        print("🔧 初始化依赖注入容器...")
        from app.container import container
        from application.character_application_service import CharacterApplicationService
        
        # 测试服务获取
        character_service = container.get(CharacterApplicationService)
        print("✅ 依赖注入容器初始化成功")
        
        # 检查PyQt5
        print("🔍 检查PyQt5...")
        try:
            from PyQt5.QtWidgets import QApplication
            print("✅ PyQt5可用")
        except ImportError:
            print("❌ PyQt5未安装")
            print("💡 请安装PyQt5: pip install PyQt5")
            return 1
        
        # 启动GUI
        print("🖥️ 启动图形界面...")
        from presentation.gui.main_window import run_gui
        
        # 运行GUI应用
        exit_code = run_gui()
        print(f"👋 应用已退出，退出代码: {exit_code}")
        return exit_code
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print(f"📁 当前工作目录: {os.getcwd()}")
        print(f"🐍 Python路径: {sys.path[:3]}...")
        print("\n💡 可能的解决方案:")
        print("   1. 确保所有依赖都已安装: pip install -r requirements.txt")
        print("   2. 检查Python环境是否正确")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
