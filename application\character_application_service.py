"""角色应用服务模块

此模块实现了角色相关的应用服务，协调用例执行。
"""

from typing import List, Optional
from .use_cases.get_characters_use_case import GetCharactersUseCase
from .use_cases.assign_cv_use_case import AssignCVUseCase
from .dto.character_dto import GetCharactersRequest, GetCharactersResponse, CharacterDTO
from .dto.assignment_dto import AssignCVRequest, AssignCVResponse


class CharacterApplicationService:
    """角色应用服务"""
    
    def __init__(self, get_characters_use_case: GetCharactersUseCase,
                 assign_cv_use_case: Optional[AssignCVUseCase] = None):
        """初始化应用服务

        Args:
            get_characters_use_case: 获取角色用例
            assign_cv_use_case: 分配CV用例，可选
        """
        self._get_characters_use_case = get_characters_use_case
        self._assign_cv_use_case = assign_cv_use_case
    
    def get_characters(self, book_id: str, include_assigned: bool = True, 
                      include_unassigned: bool = True) -> GetCharactersResponse:
        """获取角色列表
        
        Args:
            book_id: 书籍ID
            include_assigned: 是否包含已分配CV的角色
            include_unassigned: 是否包含未分配CV的角色
            
        Returns:
            GetCharactersResponse: 角色列表响应
        """
        request = GetCharactersRequest(
            book_id=book_id,
            include_assigned=include_assigned,
            include_unassigned=include_unassigned
        )
        return self._get_characters_use_case.execute(request)
    
    def assign_cv(self, character_id: str, cv_id: str, book_id: str) -> AssignCVResponse:
        """分配CV给角色

        Args:
            character_id: 角色ID
            cv_id: CV ID
            book_id: 书籍ID

        Returns:
            AssignCVResponse: 分配结果响应
        """
        if self._assign_cv_use_case is None:
            return AssignCVResponse(
                success=False,
                error_message="分配CV功能尚未实现"
            )

        request = AssignCVRequest(
            character_id=character_id,
            cv_id=cv_id,
            book_id=book_id
        )
        return self._assign_cv_use_case.execute(request)
    
    def get_unassigned_characters(self, book_id: str) -> List[CharacterDTO]:
        """获取未分配CV的角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CharacterDTO]: 未分配CV的角色列表
        """
        response = self.get_characters(book_id, include_assigned=False, include_unassigned=True)
        return response.characters if response.success else []
    
    def get_assigned_characters(self, book_id: str) -> List[CharacterDTO]:
        """获取已分配CV的角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[CharacterDTO]: 已分配CV的角色列表
        """
        response = self.get_characters(book_id, include_assigned=True, include_unassigned=False)
        return response.characters if response.success else []
