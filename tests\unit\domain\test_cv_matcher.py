"""CV匹配器单元测试"""

import pytest
from unittest.mock import Mock
from domain.services.cv_matcher import <PERSON>V<PERSON>atcher
from domain.entities.cv import CV
from domain.value_objects.cv_id import CVID
from domain.value_objects.match_result import MatchResult, MatchType


class TestCVMatcher:
    """CV匹配器测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.nickname_repo = Mock()
        self.cv_matcher = CVMatcher(self.nickname_repo)
        
        # 创建测试CV列表
        self.cvs = [
            CV(id=CVID.from_string("1"), name="张三", book_id="book_001"),
            CV(id=CVID.from_string("2"), name="李四", book_id="book_001"),
            CV(id=CVID.from_string("3"), name="王五", book_id="book_001"),
        ]
    
    def test_direct_match(self):
        """测试直接匹配"""
        result = self.cv_matcher.match_cv("张三", self.cvs)
        
        assert result.is_successful()
        assert result.is_direct_match()
        assert result.cv_id == CVID.from_string("1")
    
    def test_nickname_match(self):
        """测试简名匹配"""
        self.nickname_repo.get_full_name.return_value = "张三"
        
        result = self.cv_matcher.match_cv("小张", self.cvs)
        
        assert result.is_successful()
        assert result.is_nickname_match()
        assert result.cv_id == CVID.from_string("1")
    
    def test_no_match(self):
        """测试无匹配"""
        self.nickname_repo.get_full_name.return_value = "不存在的名字"
        
        result = self.cv_matcher.match_cv("不存在的名字", self.cvs)
        
        assert not result.is_successful()
        assert result.is_no_match()
        assert result.cv_id is None
    
    def test_unassigned_cv(self):
        """测试未分配CV"""
        result = self.cv_matcher.match_cv("—", self.cvs)
        
        assert not result.is_successful()
        assert result.message == "未分配CV"
