#!/usr/bin/env python3
"""
GUI拼音筛选功能测试脚本

验证GUI中的拼音筛选功能是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_gui_startup():
    """测试GUI启动"""
    print("🔍 测试GUI启动...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from presentation.gui.main_window import MainWindow
        
        # 创建应用实例
        app = QApplication([])
        
        # 创建主窗口
        window = MainWindow()
        print("✅ GUI主窗口创建成功")
        
        # 检查筛选器是否存在
        has_character_filter = hasattr(window, 'character_filter_group')
        has_cv_filter = hasattr(window, 'cv_filter_group')
        
        print(f"📋 角色筛选器: {'✅ 存在' if has_character_filter else '❌ 不存在'}")
        print(f"📋 CV筛选器: {'✅ 存在' if has_cv_filter else '❌ 不存在'}")
        
        if has_character_filter:
            char_buttons = window.character_filter_group.buttons()
            print(f"🔘 角色筛选按钮数量: {len(char_buttons)}")
            
            # 检查按钮文本
            button_texts = [btn.text() for btn in char_buttons[:5]]  # 只显示前5个
            print(f"🔘 前5个按钮: {button_texts}")
        
        if has_cv_filter:
            cv_buttons = window.cv_filter_group.buttons()
            print(f"🔘 CV筛选按钮数量: {len(cv_buttons)}")
        
        # 清理
        window.close()
        app.quit()
        
        return has_character_filter and has_cv_filter
        
    except Exception as e:
        print(f"❌ GUI启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pinyin_integration():
    """测试拼音功能集成"""
    print("\n🔍 测试拼音功能集成...")
    
    try:
        from utils.pinyin_helper import PinyinHelper
        
        # 测试常见角色名
        test_names = ["张小雨", "李明轩", "王诗涵", "主角", "女主角", "配角A"]
        
        print("📋 拼音首字母提取测试:")
        letter_counts = {}
        
        for name in test_names:
            letter = PinyinHelper.get_first_letter(name)
            print(f"  {name} → {letter}")
            
            if letter:
                letter_counts[letter] = letter_counts.get(letter, 0) + 1
        
        print(f"\n📊 字母分布: {letter_counts}")
        
        # 测试筛选功能
        print("\n🔍 筛选功能测试:")
        for letter in ['Z', 'L', 'W']:
            filtered = PinyinHelper.filter_by_letter(test_names, letter)
            print(f"  {letter}字母: {filtered}")
        
        return True
        
    except Exception as e:
        print(f"❌ 拼音功能集成测试失败: {e}")
        return False

def test_gui_features():
    """测试GUI功能特性"""
    print("\n🔍 测试GUI功能特性...")
    
    features = {
        "拼音助手模块": "utils/pinyin_helper.py",
        "角色筛选器": "角色列表上方的拼音筛选按钮",
        "CV筛选器": "CV选择区域的拼音筛选按钮",
        "实时筛选": "点击按钮立即更新显示",
        "状态反馈": "状态栏显示筛选结果",
        "按钮样式": "统一的筛选按钮设计"
    }
    
    print("📋 功能特性清单:")
    for feature, description in features.items():
        print(f"  ✅ {feature}: {description}")
    
    print("\n🎯 使用说明:")
    print("  1. 启动应用: python run_gui.py")
    print("  2. 选择'使用模拟数据'")
    print("  3. 选择任意书籍并加载角色")
    print("  4. 点击角色列表上方的字母按钮")
    print("  5. 观察角色表格的筛选结果")
    print("  6. 点击CV选择区域的字母按钮")
    print("  7. 观察CV下拉框的筛选结果")
    
    return True

def test_expected_behavior():
    """测试预期行为"""
    print("\n🔍 测试预期行为...")
    
    behaviors = [
        "点击'全部'按钮显示所有项目",
        "点击'Z'按钮只显示张、赵、周、主等开头的项目",
        "点击'L'按钮只显示李、刘、林等开头的项目",
        "状态栏显示筛选结果统计",
        "无匹配项时显示相应提示",
        "筛选器按钮有选中状态高亮",
        "角色和CV筛选器独立工作"
    ]
    
    print("📋 预期行为清单:")
    for i, behavior in enumerate(behaviors, 1):
        print(f"  {i}. {behavior}")
    
    print("\n⚠️ 注意事项:")
    print("  - 拼音识别基于常用汉字映射表")
    print("  - 不在映射表中的字符可能无法识别")
    print("  - 英文字符会直接按字母筛选")
    print("  - 筛选是实时的，无需刷新")
    
    return True

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - GUI拼音筛选功能测试")
    print("=" * 50)
    
    # 测试GUI启动
    gui_success = test_gui_startup()
    
    # 测试拼音功能集成
    pinyin_success = test_pinyin_integration()
    
    # 测试GUI功能特性
    features_success = test_gui_features()
    
    # 测试预期行为
    behavior_success = test_expected_behavior()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - GUI启动测试: {'✅ 通过' if gui_success else '❌ 失败'}")
    print(f"  - 拼音功能集成: {'✅ 通过' if pinyin_success else '❌ 失败'}")
    print(f"  - GUI功能特性: {'✅ 通过' if features_success else '❌ 失败'}")
    print(f"  - 预期行为测试: {'✅ 通过' if behavior_success else '❌ 失败'}")
    
    if all([gui_success, pinyin_success, features_success, behavior_success]):
        print("\n🎉 所有测试通过！GUI拼音筛选功能正常。")
        print("\n🚀 现在可以启动GUI进行实际测试:")
        print("   python run_gui.py")
        print("\n💡 测试建议:")
        print("   1. 加载包含多个角色的书籍")
        print("   2. 逐个点击不同的字母按钮")
        print("   3. 观察筛选结果是否正确")
        print("   4. 检查状态栏的统计信息")
        print("   5. 验证CV筛选器的工作情况")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
