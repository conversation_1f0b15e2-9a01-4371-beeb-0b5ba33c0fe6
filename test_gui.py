#!/usr/bin/env python3
"""
GUI测试脚本

测试PyQt5是否能正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_pyqt5():
    """测试PyQt5基本功能"""
    try:
        print("🔍 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        print("✅ PyQt5导入成功")
        
        print("🔍 创建应用程序...")
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        print("🔍 创建主窗口...")
        window = QMainWindow()
        window.setWindowTitle("CV分配工具测试")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("🎉 CV分配工具GUI测试成功!")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; padding: 20px;")
        layout.addWidget(label)
        
        info_label = QLabel(
            "这是CV分配工具的图形界面测试。\n\n"
            "如果您看到这个窗口，说明PyQt5工作正常。\n"
            "关闭此窗口以继续。"
        )
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; padding: 10px;")
        layout.addWidget(info_label)
        
        print("✅ 主窗口创建成功")
        
        print("🚀 显示窗口...")
        window.show()
        
        print("🔄 启动事件循环...")
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        print("💡 请安装PyQt5: pip install PyQt5")
        return 1
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_container():
    """测试依赖注入容器"""
    try:
        print("🔍 测试容器导入...")
        from app.container import container
        from application.character_application_service import CharacterApplicationService
        print("✅ 容器导入成功")
        
        print("🔍 测试服务获取...")
        service = container.get(CharacterApplicationService)
        print("✅ 服务获取成功")
        
        return True
    except Exception as e:
        print(f"❌ 容器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具GUI测试")
    print("=" * 40)
    
    # 测试容器
    print("\n📦 测试依赖注入容器...")
    if not test_container():
        return 1
    
    # 测试GUI
    print("\n🖥️ 测试图形界面...")
    return test_pyqt5()

if __name__ == "__main__":
    sys.exit(main())
