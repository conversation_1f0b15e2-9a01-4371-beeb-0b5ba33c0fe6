"""GStudios API服务实现模块

此模块实现了基于GStudios API的服务，属于基础设施层。
"""

from typing import List, Dict, Tuple, Any
from .api_service_interface import APIServiceInterface
from .gstudios_client import GStudiosAPIClient, APIConfig


class GStudiosAPIService(APIServiceInterface):
    """GStudios API服务实现
    
    实现了APIServiceInterface接口，提供基于GStudios API的具体实现。
    """
    
    def __init__(self, base_url: str, token: str = None):
        """初始化API服务
        
        Args:
            base_url: API基础URL
            token: 认证令牌
        """
        config = APIConfig(base_url, token)
        self._client = GStudiosAPIClient(config)
    
    def set_token(self, token: str) -> None:
        """设置认证令牌
        
        Args:
            token: 认证令牌
        """
        self._client.set_token(token)
    
    def get_books(self, finished: str = "all") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
                - "all": 获取所有书籍
                - "finished": 获取已完成的书籍
                - "unfinished": 获取未完成的书籍
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 书籍列表)
        """
        return self._client.get_books(finished)
    
    def get_characters(self, book_id: str, **filters) -> Tuple[bool, List[Dict[str, Any]]]:
        """获取角色列表
        
        Args:
            book_id: 书籍ID
            **filters: 筛选参数，如：
                - character_id: 角色ID
                - query_text: 查询文本
                - gender: 性别筛选
                - type_param: 类型筛选
                - age_type: 年龄类型筛选
                - cv_robot_id: AI配音ID筛选
                - cv_human_id: 人工配音ID筛选
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 角色列表)
        """
        return self._client.get_characters(book_id, **filters)
    
    def get_cvs(self, book_id: str, cv_type: str = "human") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取CV列表
        
        Args:
            book_id: 书籍ID
            cv_type: CV类型 ("human" 或 "robot")
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, CV列表)
        """
        return self._client.get_cvs(book_id, cv_type)
    
    def assign_cv_to_character(self, character_id: str, cv_id: str) -> Tuple[bool, str]:
        """分配CV给角色
        
        Args:
            character_id: 角色ID
            cv_id: CV ID
        
        Returns:
            Tuple[bool, str]: (成功标志, 成功/错误消息)
        """
        return self._client.assign_cv_to_character(character_id, cv_id)
    
    def get_partners(self, book_id: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """获取合作伙伴列表
        
        Args:
            book_id: 书籍ID
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 合作伙伴列表)
        """
        return self._client.get_partners(book_id)
    
    def invite_partner(self, book_id: str, subject_id: str, price: float) -> Tuple[bool, str]:
        """邀请合作伙伴
        
        Args:
            book_id: 书籍ID
            subject_id: 合作伙伴ID
            price: 价格
        
        Returns:
            Tuple[bool, str]: (成功标志, 成功/错误消息)
        """
        return self._client.invite_partner(book_id, subject_id, price)
