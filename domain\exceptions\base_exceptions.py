"""领域异常基类模块"""


class DomainException(Exception):
    """领域异常基类
    
    所有领域层异常的基类，用于标识业务逻辑相关的异常。
    """
    
    def __init__(self, message: str, error_code: str = None):
        """初始化领域异常
        
        Args:
            message: 异常消息
            error_code: 错误代码，可选
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
