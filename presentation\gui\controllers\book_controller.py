"""书籍控制器模块

此模块实现了书籍相关的控制器逻辑。
"""

from typing import List
from .base_controller import BaseController
from application.book_application_service import BookApplicationService
from application.dto.book_dto import BookDTO


class BookController(BaseController):
    """书籍控制器"""
    
    def __init__(self):
        """初始化书籍控制器"""
        super().__init__()
        self._book_service = self.get_service(BookApplicationService)
    
    def get_books(self, finished: str = "all") -> List[BookDTO]:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
            
        Returns:
            List[BookDTO]: 书籍列表
        """
        try:
            response = self._book_service.get_books(finished)
            if response.success:
                return response.books
            else:
                print(f"获取书籍失败: {response.error_message}")
                return []
        except Exception as e:
            print(f"获取书籍时发生错误: {e}")
            return []
    
    def get_all_books(self) -> List[BookDTO]:
        """获取所有书籍"""
        return self.get_books("all")
    
    def get_finished_books(self) -> List[BookDTO]:
        """获取已完成的书籍"""
        return self.get_books("finished")
    
    def get_unfinished_books(self) -> List[BookDTO]:
        """获取未完成的书籍"""
        return self.get_books("unfinished")
