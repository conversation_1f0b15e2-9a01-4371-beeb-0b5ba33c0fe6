"""获取书籍列表用例模块

此模块实现了获取书籍列表的应用用例。
"""

from typing import List
from application.use_cases.base_use_case import UseCase
from application.dto.book_dto import BookDTO, GetBooksRequest, GetBooksResponse
from infrastructure.api.api_service_interface import APIServiceInterface


class GetBooksUseCase(UseCase[GetBooksRequest, GetBooksResponse]):
    """获取书籍列表用例"""
    
    def __init__(self, api_service: APIServiceInterface):
        """初始化用例
        
        Args:
            api_service: API服务接口
        """
        self._api_service = api_service
    
    def execute(self, request: GetBooksRequest) -> GetBooksResponse:
        """执行获取书籍列表用例
        
        Args:
            request: 获取书籍请求
            
        Returns:
            GetBooksResponse: 书籍列表响应
        """
        try:
            # 调用API服务获取书籍数据
            success, raw_books = self._api_service.get_books(request.finished)
            
            if not success:
                return GetBooksResponse.error_response("获取书籍列表失败")
            
            # 转换为DTO对象
            books = []
            for raw_book in raw_books:
                book_dto = self._convert_to_dto(raw_book)
                if book_dto:
                    books.append(book_dto)
            
            return GetBooksResponse.success_response(books, len(books))
            
        except Exception as e:
            return GetBooksResponse.error_response(f"获取书籍列表时发生错误: {str(e)}")
    
    def _convert_to_dto(self, raw_book: dict) -> BookDTO:
        """将原始书籍数据转换为DTO
        
        Args:
            raw_book: 原始书籍数据
            
        Returns:
            BookDTO: 书籍DTO对象
        """
        try:
            return BookDTO(
                id=str(raw_book.get('id', '')),
                name=raw_book.get('name', '未知书籍'),
                description=raw_book.get('remark'),
                finished=raw_book.get('finished', False),
                author=raw_book.get('author'),
                created_at=raw_book.get('createTime'),
                updated_at=raw_book.get('updateTime')
            )
        except Exception as e:
            print(f"转换书籍数据时发生错误: {e}, 原始数据: {raw_book}")
            return None
