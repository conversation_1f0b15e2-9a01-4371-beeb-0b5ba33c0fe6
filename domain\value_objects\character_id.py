"""角色ID值对象模块"""

from dataclasses import dataclass
from typing import Union


@dataclass(frozen=True)
class CharacterID:
    """角色ID值对象
    
    表示角色的唯一标识符，确保类型安全和值的不可变性。
    
    Attributes:
        value: ID的字符串值
    """
    
    value: str
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.value or not isinstance(self.value, str):
            raise ValueError("角色ID必须是非空字符串")
        
        # 规范化ID值（去除首尾空格）
        normalized_value = self.value.strip()
        if not normalized_value:
            raise ValueError("角色ID不能为空或只包含空格")
        
        # 使用object.__setattr__因为dataclass是frozen的
        object.__setattr__(self, 'value', normalized_value)
    
    @classmethod
    def from_string(cls, id_str: str) -> 'CharacterID':
        """从字符串创建角色ID
        
        Args:
            id_str: ID字符串
            
        Returns:
            CharacterID: 角色ID实例
        """
        return cls(id_str)
    
    @classmethod
    def from_int(cls, id_int: int) -> 'CharacterID':
        """从整数创建角色ID
        
        Args:
            id_int: ID整数
            
        Returns:
            CharacterID: 角色ID实例
        """
        return cls(str(id_int))
    
    def to_string(self) -> str:
        """转换为字符串
        
        Returns:
            str: ID的字符串表示
        """
        return self.value
    
    def to_int(self) -> int:
        """转换为整数
        
        Returns:
            int: ID的整数表示
            
        Raises:
            ValueError: 当ID不能转换为整数时
        """
        try:
            return int(self.value)
        except ValueError:
            raise ValueError(f"角色ID '{self.value}' 不能转换为整数")
    
    def is_numeric(self) -> bool:
        """检查ID是否为数字
        
        Returns:
            bool: 是否为数字ID
        """
        try:
            int(self.value)
            return True
        except ValueError:
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return self.value
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return f"CharacterID('{self.value}')"
    
    def __eq__(self, other) -> bool:
        """相等性比较"""
        if isinstance(other, CharacterID):
            return self.value == other.value
        elif isinstance(other, str):
            return self.value == other
        return False
    
    def __hash__(self) -> int:
        """哈希值"""
        return hash(self.value)
    
    def __lt__(self, other) -> bool:
        """小于比较 - 用于排序"""
        if isinstance(other, CharacterID):
            return self.value < other.value
        elif isinstance(other, str):
            return self.value < other
        return NotImplemented
