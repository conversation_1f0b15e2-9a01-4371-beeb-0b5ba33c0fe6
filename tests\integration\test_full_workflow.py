"""集成测试模块

此模块包含系统集成测试。
"""

import pytest
from app.container import container
from application.character_application_service import CharacterApplicationService


class TestIntegration:
    """集成测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.character_service = container.get(CharacterApplicationService)
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 这里应该包含完整的端到端测试
        # 由于需要真实的API连接，这里只做示例
        
        # 1. 获取角色列表
        response = self.character_service.get_characters("test_book_id")
        
        # 2. 验证响应结构
        assert hasattr(response, 'success')
        assert hasattr(response, 'characters')
        
        # 3. 如果有角色，测试分配功能
        if response.success and response.characters:
            character = response.characters[0]
            # 这里可以添加更多的集成测试逻辑
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的书籍ID
        response = self.character_service.get_characters("invalid_book_id")
        
        # 应该优雅地处理错误
        assert hasattr(response, 'success')
        if not response.success:
            assert hasattr(response, 'error_message')
