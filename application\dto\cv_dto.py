"""CV数据传输对象模块

此模块定义了CV相关的数据传输对象。
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class CVDTO:
    """CV数据传输对象"""
    id: str
    name: str
    book_id: str
    is_available: bool = True
    type: str = 'human'
    description: Optional[str] = None
    nicknames: List[str] = None
    
    def __post_init__(self):
        if self.nicknames is None:
            self.nicknames = []


@dataclass
class GetCVsRequest:
    """获取CV列表请求"""
    book_id: str
    cv_type: str = 'human'
    include_unavailable: bool = False


@dataclass
class GetCVsResponse:
    """获取CV列表响应"""
    success: bool
    cvs: List[CVDTO]
    total_count: int
    error_message: Optional[str] = None
