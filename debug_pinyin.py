#!/usr/bin/env python3
"""
拼音功能调试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from utils.pinyin_helper import PinyinHelper

def debug_pinyin():
    """调试拼音功能"""
    test_names = ["张小雨", "李明轩", "王诗涵", "主角", "女主角"]

    print("调试拼音首字母提取:")
    for name in test_names:
        first_char = name[0] if name else ""
        letter = PinyinHelper.get_first_letter(name)
        dict_value = PinyinHelper.PINYIN_DICT.get(first_char, "未找到")

        print(f"姓名: {name}")
        print(f"  首字符: {first_char}")
        print(f"  字典值: {dict_value}")
        print(f"  返回值: {letter}")
        print(f"  类型: {type(letter)}")

        # 直接测试字典查找
        direct_lookup = PinyinHelper.PINYIN_DICT.get(first_char)
        print(f"  直接查找: {direct_lookup}")
        print()

    # 测试筛选功能
    print("测试筛选功能:")
    test_items = ["张小雨", "李明轩", "王诗涵", "主角"]

    # 测试Z字母筛选
    z_items = PinyinHelper.filter_by_letter(test_items, "Z")
    print(f"Z字母筛选结果: {z_items}")

    # 测试L字母筛选
    l_items = PinyinHelper.filter_by_letter(test_items, "L")
    print(f"L字母筛选结果: {l_items}")

if __name__ == "__main__":
    debug_pinyin()
