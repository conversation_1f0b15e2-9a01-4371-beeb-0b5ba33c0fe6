#!/usr/bin/env python3
"""
CV分配工具启动脚本

这个脚本用于正确启动CV分配工具，处理Python模块导入问题。
"""

import sys
import os
from pathlib import Path

def main():
    """主启动函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 确保项目根目录在Python路径中
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # 设置工作目录
    os.chdir(project_root)
    
    try:
        # 导入并运行主应用
        from app.main import main as app_main
        return app_main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print(f"📁 当前工作目录: {os.getcwd()}")
        print(f"🐍 Python路径: {sys.path[:3]}...")  # 只显示前3个路径
        print("\n💡 请确保所有依赖都已正确安装:")
        print("   pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
