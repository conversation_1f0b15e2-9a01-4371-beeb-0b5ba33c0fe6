# CV分配工具 - 独立分离架构版本

## 🎯 项目概述

这是角色CV分配工具的独立分离架构版本，采用清洁架构（Clean Architecture）和领域驱动设计（DDD）原则重构而成。

## 🏗️ 架构特点

- **清洁架构**: 依赖倒置，业务逻辑与技术实现分离
- **领域驱动设计**: 业务概念直接映射到代码结构
- **依赖注入**: 松耦合的组件关系
- **用例模式**: 清晰的业务操作定义
- **完全独立**: 所有代码组织在独立目录中

## 📁 目录结构

```
cv_assignment_tool/
├── app/                    # 应用程序层
├── domain/                 # 领域层 (核心业务逻辑)
├── application/            # 应用服务层
├── infrastructure/         # 基础设施层
├── presentation/           # 表示层
├── shared/                 # 共享组件
├── tests/                  # 测试
└── docs/                   # 文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd cv_assignment_tool
pip install -r requirements.txt
```

### 2. 运行应用

#### GUI模式（推荐）
```bash
python run_gui.py
```

#### 控制台模式
```bash
python app/main.py
```

### 3. 运行测试

```bash
pytest tests/
```

## 📚 文档

- [架构文档](docs/architecture/)
- [API文档](docs/api/)
- [用户指南](docs/user_guide/)
- [开发者文档](docs/developer/)

## 🔧 开发

### 代码质量检查

```bash
# 代码格式化
black .
isort .

# 类型检查
mypy domain/ application/

# 代码风格检查
flake8 .

# 测试覆盖率
pytest --cov=. --cov-report=html
```

## 📄 许可证

MIT License
