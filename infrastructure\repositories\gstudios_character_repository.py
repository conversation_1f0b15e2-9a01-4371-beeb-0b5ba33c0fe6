"""角色仓储实现模块

此模块实现了基于GStudios API的角色仓储。
"""

from typing import List, Optional
from domain.entities.character import Character
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID
from domain.repositories.character_repository import CharacterRepository
from infrastructure.api.api_service_interface import APIServiceInterface


class GStudiosCharacterRepository(CharacterRepository):
    """基于GStudios API的角色仓储实现"""
    
    def __init__(self, api_service: APIServiceInterface):
        """初始化角色仓储
        
        Args:
            api_service: API服务接口
        """
        self._api_service = api_service
    
    def get_by_id(self, character_id: CharacterID) -> Optional[Character]:
        """根据ID获取角色"""
        # 实现逻辑...
        pass
    
    def get_by_book_id(self, book_id: str) -> List[Character]:
        """根据书籍ID获取角色列表"""
        success, raw_characters = self._api_service.get_characters(book_id)
        if not success:
            return []

        characters = []
        for raw_char in raw_characters:
            character = Character(
                id=CharacterID.from_string(str(raw_char['id'])),
                name=raw_char['name'],
                book_id=book_id,
                cv_id=CVID.from_string(str(raw_char['cvHumanId'])) if raw_char.get('cvHumanId') else None,
                description=raw_char.get('description')
            )
            characters.append(character)

        return characters

    def get_characters_with_cv_names(self, book_id: str) -> List[tuple]:
        """获取角色列表及其CV名字信息

        Returns:
            List[tuple]: (Character, cv_name) 的列表
        """
        success, raw_characters = self._api_service.get_characters(book_id)

        if not success:
            return []

        characters_with_cv_names = []
        for raw_char in raw_characters:
            character = Character(
                id=CharacterID.from_string(str(raw_char['id'])),
                name=raw_char['name'],
                book_id=book_id,
                cv_id=CVID.from_string(str(raw_char['cvHumanId'])) if raw_char.get('cvHumanId') else None,
                description=raw_char.get('description')
            )

            # 获取CV名字
            cv_name = raw_char.get('cvHumanName') if raw_char.get('cvHumanId') else None

            characters_with_cv_names.append((character, cv_name))

        return characters_with_cv_names
    
    def get_by_name(self, name: str, book_id: str) -> Optional[Character]:
        """根据名称和书籍ID获取角色"""
        characters = self.get_by_book_id(book_id)
        for character in characters:
            if character.name == name:
                return character
        return None
    
    def get_unassigned_characters(self, book_id: str) -> List[Character]:
        """获取未分配CV的角色列表"""
        characters = self.get_by_book_id(book_id)
        return [char for char in characters if not char.is_cv_assigned()]
    
    def get_assigned_characters(self, book_id: str) -> List[Character]:
        """获取已分配CV的角色列表"""
        characters = self.get_by_book_id(book_id)
        return [char for char in characters if char.is_cv_assigned()]
    
    def save(self, character: Character) -> None:
        """保存角色"""
        # 实现保存逻辑...
        pass
    
    def save_all(self, characters: List[Character]) -> None:
        """批量保存角色"""
        for character in characters:
            self.save(character)
    
    def update_cv_assignment(self, character_id: CharacterID, cv_id: CVID) -> bool:
        """更新角色的CV分配"""
        success, message = self._api_service.assign_cv_to_character(
            character_id.to_string(), cv_id.to_string()
        )
        return success
    
    def remove_cv_assignment(self, character_id: CharacterID) -> bool:
        """移除角色的CV分配"""
        # 实现移除逻辑...
        return True
    
    def exists(self, character_id: CharacterID) -> bool:
        """检查角色是否存在"""
        character = self.get_by_id(character_id)
        return character is not None
    
    def count_by_book_id(self, book_id: str) -> int:
        """统计指定书籍的角色数量"""
        characters = self.get_by_book_id(book_id)
        return len(characters)
    
    def find_by_cv_id(self, cv_id: CVID, book_id: str) -> List[Character]:
        """根据CV ID查找角色"""
        characters = self.get_by_book_id(book_id)
        return [char for char in characters if char.cv_id == cv_id]
