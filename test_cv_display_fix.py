#!/usr/bin/env python3
"""
CV显示修复测试脚本

测试修复后的CV名字显示逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_cv_display_logic():
    """测试CV显示逻辑"""
    print("🔍 测试修复后的CV显示逻辑...")
    
    # 模拟不同的CV数据结构
    test_cases = [
        # 模拟数据格式
        {"id": "cv_001", "name": "张小雨", "description": "温柔甜美"},
        {"id": "cv_002", "name": "李明轩", "description": "阳光帅气"},
        
        # 真实API数据格式
        {"cvId": 379, "cvName": "兼职账号2", "price": 200},
        {"cvId": 12, "cvName": "兼职账号", "price": 120000},
        {"cvId": 16, "cvName": "山竹超超好吃", "price": 888800},
        
        # 边界情况
        {"id": "cv_003"},  # 只有ID
        {"name": "王诗涵"},  # 只有名字
        {"cvId": 999},  # 只有cvId
        {"cvName": "测试CV"},  # 只有cvName
        {},  # 空数据
    ]
    
    print("\n📋 CV显示逻辑测试结果:")
    print("=" * 80)
    print(f"{'原始数据':<40} {'显示名称':<20} {'存储ID'}")
    print("-" * 80)
    
    for cv in test_cases:
        # 使用修复后的逻辑
        cv_name = (cv.get('name') or cv.get('cvName') or 
                  cv.get('id') or str(cv.get('cvId')) or '未知CV')
        cv_id = cv.get('id') or str(cv.get('cvId')) or None
        
        # 截断显示原始数据
        cv_str = str(cv)
        if len(cv_str) > 38:
            cv_str = cv_str[:35] + "..."
        
        print(f"{cv_str:<40} {cv_name:<20} {cv_id}")
    
    print("=" * 80)
    return True

def test_real_api_data():
    """测试真实API数据"""
    print("\n🔍 测试真实API数据处理...")
    
    try:
        from app.container import container
        from infrastructure.api.gstudios_api_service import GStudiosAPIService
        
        # 获取API服务
        api_service = container.get(GStudiosAPIService)
        
        # 获取CV数据
        success, cvs = api_service.get_cvs("1", "human")
        
        if success and cvs:
            print(f"✅ 获取到 {len(cvs)} 个真实CV数据")
            print("\n📋 真实CV数据处理结果:")
            print("=" * 60)
            print(f"{'CV名称':<20} {'CV ID':<10} {'价格':<10}")
            print("-" * 60)
            
            for cv in cvs[:5]:  # 只显示前5个
                cv_name = (cv.get('name') or cv.get('cvName') or 
                          cv.get('id') or str(cv.get('cvId')) or '未知CV')
                cv_id = cv.get('id') or str(cv.get('cvId')) or 'N/A'
                price = cv.get('price', 'N/A')
                
                print(f"{cv_name:<20} {cv_id:<10} {price}")
            
            print("=" * 60)
            return True
        else:
            print("❌ 未获取到真实CV数据")
            return False
        
    except Exception as e:
        print(f"❌ 真实API数据测试失败: {e}")
        return False

def test_mock_data():
    """测试模拟数据"""
    print("\n🔍 测试模拟数据处理...")
    
    try:
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        # 创建API客户端（无令牌，使用模拟数据）
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        
        # 获取CV数据
        success, cvs = client.get_cvs("1", "human")
        
        if success:
            print(f"✅ 获取到 {len(cvs)} 个模拟CV数据")
            print("\n📋 模拟CV数据处理结果:")
            print("=" * 60)
            print(f"{'CV名称':<15} {'CV ID':<10} {'性别':<6} {'年龄'}")
            print("-" * 60)
            
            for cv in cvs:
                cv_name = (cv.get('name') or cv.get('cvName') or 
                          cv.get('id') or str(cv.get('cvId')) or '未知CV')
                cv_id = cv.get('id') or str(cv.get('cvId')) or 'N/A'
                gender = cv.get('gender', 'N/A')
                age = cv.get('age', 'N/A')
                
                print(f"{cv_name:<15} {cv_id:<10} {gender:<6} {age}")
            
            print("=" * 60)
            return True
        else:
            print("❌ 模拟数据获取失败")
            return False
        
    except Exception as e:
        print(f"❌ 模拟数据测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 CV显示修复测试")
    print("=" * 50)
    
    # 测试显示逻辑
    logic_success = test_cv_display_logic()
    
    # 测试真实API数据
    real_success = test_real_api_data()
    
    # 测试模拟数据
    mock_success = test_mock_data()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - CV显示逻辑: {'✅ 通过' if logic_success else '❌ 失败'}")
    print(f"  - 真实API数据: {'✅ 通过' if real_success else '❌ 失败'}")
    print(f"  - 模拟数据处理: {'✅ 通过' if mock_success else '❌ 失败'}")
    
    if all([logic_success, real_success, mock_success]):
        print("\n🎉 CV显示修复成功！")
        print("\n💡 修复说明:")
        print("  - 兼容模拟数据格式 (id, name)")
        print("  - 兼容真实API格式 (cvId, cvName)")
        print("  - 智能回退机制，避免显示'未知CV'")
        print("  - 正确提取和存储CV ID")
        
        print("\n🚀 启动GUI验证:")
        print("   python run_gui.py")
        print("   → 选择书籍并加载角色")
        print("   → 查看CV下拉框应显示真实CV名字")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查修复逻辑。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
