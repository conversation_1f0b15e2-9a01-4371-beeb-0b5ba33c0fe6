"""角色实体模块

此模块定义了角色(Character)实体，包含角色的基本信息和业务行为。
"""

from dataclasses import dataclass
from typing import Optional
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID
from domain.exceptions.character_exceptions import InvalidCharacterError


@dataclass
class Character:
    """角色实体
    
    角色是系统的核心实体之一，代表需要分配CV的角色。
    
    Attributes:
        id: 角色唯一标识符
        name: 角色名称
        cv_id: 分配的CV标识符，可选
        book_id: 所属书籍标识符
        description: 角色描述，可选
    """
    
    id: CharacterID
    name: str
    book_id: str
    cv_id: Optional[CVID] = None
    description: Optional[str] = None
    
    def __post_init__(self):
        """初始化后验证"""
        self._validate()
    
    def _validate(self) -> None:
        """验证角色数据的有效性"""
        if not self.name or not self.name.strip():
            raise InvalidCharacterError("角色名称不能为空")
        
        if not self.book_id or not self.book_id.strip():
            raise InvalidCharacterError("书籍ID不能为空")
        
        # 规范化名称（去除首尾空格）
        object.__setattr__(self, 'name', self.name.strip())
    
    def assign_cv(self, cv_id: CVID) -> None:
        """分配CV给角色
        
        这是角色实体的核心业务行为，包含分配CV的业务规则。
        
        Args:
            cv_id: 要分配的CV标识符
            
        Raises:
            InvalidCharacterError: 当CV ID无效时抛出
        """
        if cv_id is None:
            raise InvalidCharacterError("CV ID不能为空")
        
        # 业务规则：记录分配历史（如果需要的话）
        old_cv_id = self.cv_id
        object.__setattr__(self, 'cv_id', cv_id)
        
        # 这里可以添加领域事件，比如：
        # self._domain_events.append(CVAssignedEvent(self.id, old_cv_id, cv_id))
    
    def unassign_cv(self) -> None:
        """取消CV分配"""
        old_cv_id = self.cv_id
        object.__setattr__(self, 'cv_id', None)
        
        # 这里可以添加领域事件，比如：
        # self._domain_events.append(CVUnassignedEvent(self.id, old_cv_id))
    
    def is_cv_assigned(self) -> bool:
        """检查是否已分配CV"""
        return self.cv_id is not None
    
    def can_assign_cv(self, cv_id: CVID) -> bool:
        """检查是否可以分配指定的CV
        
        Args:
            cv_id: 要检查的CV标识符
            
        Returns:
            bool: 是否可以分配
        """
        if cv_id is None:
            return False
        
        # 业务规则：可以重新分配CV
        return True
    
    def get_display_name(self) -> str:
        """获取显示名称
        
        Returns:
            str: 用于显示的角色名称
        """
        return self.name
    
    def __str__(self) -> str:
        """字符串表示"""
        cv_info = f" (CV: {self.cv_id})" if self.cv_id else " (未分配CV)"
        return f"角色[{self.name}]{cv_info}"
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return (f"Character(id={self.id!r}, name={self.name!r}, "
                f"book_id={self.book_id!r}, cv_id={self.cv_id!r})")
    
    def __eq__(self, other) -> bool:
        """相等性比较 - 基于ID"""
        if not isinstance(other, Character):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """哈希值 - 基于ID"""
        return hash(self.id)
