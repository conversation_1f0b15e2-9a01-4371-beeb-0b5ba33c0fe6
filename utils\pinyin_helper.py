"""拼音处理工具模块

此模块提供中文拼音首字母提取功能。
"""

import re
from typing import Optional


class PinyinHelper:
    """拼音处理助手类"""
    
    # 常用汉字拼音首字母映射表（简化版）
    PINYIN_DICT = {
        # A
        '阿': 'A', '安': 'A', '爱': 'A', '艾': 'A', '奥': 'A',
        # B
        '白': 'B', '百': 'B', '北': 'B', '本': 'B', '比': 'B', '边': 'B', '别': 'B', '宾': 'B',
        # C
        '曹': 'C', '陈': 'C', '成': 'C', '程': 'C', '崔': 'C', '春': 'C', '慈': 'C', '聪': 'C',
        # D
        '大': 'D', '丹': 'D', '邓': 'D', '丁': 'D', '东': 'D', '杜': 'D', '段': 'D', '董': 'D',
        # E
        '恩': 'E', '二': 'E',
        # F
        '方': 'F', '范': 'F', '费': 'F', '冯': 'F', '傅': 'F', '福': 'F', '凤': 'F', '风': 'F', '反': 'F',
        # G
        '高': 'G', '郭': 'G', '关': 'G', '顾': 'G', '龚': 'G', '宫': 'G', '古': 'G', '谷': 'G',
        # H
        '韩': 'H', '何': 'H', '黄': 'H', '胡': 'H', '华': 'H', '洪': 'H', '侯': 'H', '和': 'H',
        # J
        '江': 'J', '金': 'J', '姜': 'J', '蒋': 'J', '贾': 'J', '焦': 'J', '纪': 'J', '季': 'J', '兼': 'J',
        # K
        '孔': 'K', '康': 'K', '柯': 'K', '可': 'K',
        # L
        '李': 'L', '刘': 'L', '林': 'L', '梁': 'L', '廖': 'L', '吕': 'L', '陆': 'L', '卢': 'L',
        '罗': 'L', '雷': 'L', '龙': 'L', '娄': 'L', '柳': 'L', '路': 'L', '鲁': 'L', '兰': 'L',
        # M
        '马': 'M', '毛': 'M', '孟': 'M', '苗': 'M', '莫': 'M', '穆': 'M', '梅': 'M', '米': 'M',
        # N
        '牛': 'N', '倪': 'N', '聂': 'N', '宁': 'N', '南': 'N', '女': 'N',
        # O
        '欧': 'O',
        # P
        '潘': 'P', '彭': 'P', '裴': 'P', '朴': 'P', '蒲': 'P', '平': 'P', '配': 'P',
        # Q
        '钱': 'Q', '秦': 'Q', '邱': 'Q', '乔': 'Q', '齐': 'Q', '祁': 'Q', '戚': 'Q', '强': 'Q',
        # R
        '任': 'R', '阮': 'R', '饶': 'R', '容': 'R', '荣': 'R', '冉': 'R',
        # S
        '孙': 'S', '宋': 'S', '苏': 'S', '沈': 'S', '石': 'S', '史': 'S', '司': 'S', '邵': 'S',
        '施': 'S', '盛': 'S', '申': 'S', '尚': 'S', '水': 'S', '山': 'S', '商': 'S', '时': 'S',
        # T
        '唐': 'T', '田': 'T', '谭': 'T', '汤': 'T', '陶': 'T', '滕': 'T', '童': 'T', '涂': 'T',
        # W
        '王': 'W', '吴': 'W', '武': 'W', '魏': 'W', '韦': 'W', '温': 'W', '万': 'W', '汪': 'W',
        '文': 'W', '翁': 'W', '卫': 'W', '伍': 'W', '巫': 'W', '吾': 'W', '午': 'W', '五': 'W',
        # X
        '徐': 'X', '许': 'X', '谢': 'X', '夏': 'X', '肖': 'X', '熊': 'X', '薛': 'X', '向': 'X',
        '项': 'X', '邢': 'X', '辛': 'X', '席': 'X', '西': 'X', '小': 'X', '新': 'X', '心': 'X', '萧': 'X',
        # Y
        '杨': 'Y', '叶': 'Y', '于': 'Y', '余': 'Y', '袁': 'Y', '姚': 'Y', '严': 'Y', '颜': 'Y',
        '尹': 'Y', '易': 'Y', '殷': 'Y', '俞': 'Y', '岳': 'Y', '云': 'Y', '应': 'Y', '游': 'Y',
        # Z
        '张': 'Z', '赵': 'Z', '周': 'Z', '朱': 'Z', '郑': 'Z', '钟': 'Z', '曾': 'Z', '左': 'Z',
        '邹': 'Z', '庄': 'Z', '章': 'Z', '詹': 'Z', '甄': 'Z', '郅': 'Z', '宗': 'Z', '祖': 'Z',
        # 其他常见字
        '主': 'Z', '中': 'Z'
    }
    
    @classmethod
    def get_first_letter(cls, text: str) -> Optional[str]:
        """获取中文文本的拼音首字母
        
        Args:
            text: 中文文本
            
        Returns:
            拼音首字母，如果无法识别则返回None
        """
        if not text:
            return None
        
        # 去除空格和特殊字符，取第一个汉字
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', '', text)
        if not clean_text:
            return None
        
        first_char = clean_text[0]

        # 如果是英文字母，直接返回大写
        if first_char.isascii() and first_char.isalpha():
            return first_char.upper()

        # 查找汉字对应的拼音首字母
        return cls.PINYIN_DICT.get(first_char, None)
    
    @classmethod
    def get_all_letters(cls) -> list:
        """获取所有可能的拼音首字母列表
        
        Returns:
            按字母顺序排列的首字母列表
        """
        return ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    
    @classmethod
    def filter_by_letter(cls, items: list, letter: str, key_func=None) -> list:
        """按拼音首字母筛选列表
        
        Args:
            items: 要筛选的项目列表
            letter: 目标首字母
            key_func: 从项目中提取文本的函数，默认直接使用项目作为文本
            
        Returns:
            筛选后的列表
        """
        if not letter or letter == '全部':
            return items
        
        filtered_items = []
        for item in items:
            text = key_func(item) if key_func else str(item)
            item_letter = cls.get_first_letter(text)
            if item_letter == letter.upper():
                filtered_items.append(item)
        
        return filtered_items


# 便捷函数
def get_pinyin_first_letter(text: str) -> Optional[str]:
    """获取中文文本的拼音首字母（便捷函数）"""
    return PinyinHelper.get_first_letter(text)


def filter_items_by_pinyin(items: list, letter: str, key_func=None) -> list:
    """按拼音首字母筛选项目（便捷函数）"""
    return PinyinHelper.filter_by_letter(items, letter, key_func)
