#!/usr/bin/env python3
"""
配置文件结构适配测试脚本

测试配置管理器是否能正确处理现有的分层配置文件结构
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_existing_config_loading():
    """测试加载现有配置文件"""
    print("🔍 测试加载现有配置文件...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        config = config_manager.load_config()
        
        print("✅ 配置加载成功")
        print(f"📋 配置详情:")
        print(f"   API基础URL: {config.api_base_url}")
        print(f"   API令牌: {'已设置' if config.api_token else '未设置'}")
        if config.api_token:
            token_preview = config.api_token[:8] + "..." if len(config.api_token) > 8 else config.api_token
            print(f"   令牌预览: {token_preview}")
        print(f"   记住令牌: {config.remember_token}")
        print(f"   窗口大小: {config.window_width}x{config.window_height}")
        print(f"   主题: {config.theme}")
        print(f"   调试模式: {config.debug_enabled}")
        print(f"   日志级别: {config.log_level}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_methods():
    """测试配置方法"""
    print("\n🔍 测试配置方法...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # 测试获取API令牌
        print("🔑 测试API令牌方法...")
        token = config_manager.get_api_token()
        print(f"   获取令牌: {'成功' if token else '无令牌'}")
        
        # 测试获取基础URL
        base_url = config_manager.get_api_base_url()
        print(f"   基础URL: {base_url}")
        
        # 测试窗口大小
        width, height = config_manager.get_window_size()
        print(f"   窗口大小: {width}x{height}")
        
        # 测试主题
        theme = config_manager.get_theme()
        print(f"   主题: {theme}")
        
        # 测试上次选择的书籍
        last_book = config_manager.get_last_selected_book()
        print(f"   上次书籍: {last_book or '无'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_save_structure():
    """测试配置保存结构"""
    print("\n🔍 测试配置保存结构...")
    
    try:
        from utils.config_manager import get_config_manager
        import json
        
        config_manager = get_config_manager()
        
        # 备份原始配置
        original_config = config_manager.load_config()
        
        # 修改一些设置
        config_manager.set_last_selected_book("test_book_123")
        config_manager.set_window_size(1400, 900)
        config_manager.set_theme("dark")
        
        # 读取保存后的配置文件
        config_path = config_manager.get_config_file_path()
        with open(config_path, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        print("💾 保存后的配置文件结构:")
        print(json.dumps(saved_data, indent=2, ensure_ascii=False))
        
        # 验证结构
        expected_sections = ['API', 'GUI', 'Debug']
        missing_sections = [section for section in expected_sections if section not in saved_data]
        
        if not missing_sections:
            print("✅ 配置文件结构正确")
            
            # 验证各部分内容
            api_section = saved_data.get('API', {})
            gui_section = saved_data.get('GUI', {})
            debug_section = saved_data.get('Debug', {})
            
            print("📋 各部分验证:")
            print(f"   API部分: {'✅' if 'base_url' in api_section else '❌'}")
            print(f"   GUI部分: {'✅' if 'window_width' in gui_section else '❌'}")
            print(f"   Debug部分: {'✅' if 'enabled' in debug_section else '❌'}")
            
            # 验证修改是否生效
            if (gui_section.get('last_selected_book') == 'test_book_123' and
                gui_section.get('window_width') == 1400 and
                gui_section.get('window_height') == 900 and
                gui_section.get('theme') == 'dark'):
                print("✅ 配置修改保存成功")
            else:
                print("❌ 配置修改保存失败")
        else:
            print(f"❌ 缺少配置部分: {missing_sections}")
            return False
        
        # 恢复原始配置
        config_manager.save_config(original_config)
        
        return True
        
    except Exception as e:
        print(f"❌ 配置保存结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_login_with_existing_token():
    """测试使用现有令牌的自动登录"""
    print("\n🔍 测试使用现有令牌的自动登录...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        existing_token = config_manager.get_api_token()
        
        if existing_token:
            print(f"✅ 发现现有令牌")
            token_preview = existing_token[:8] + "..." if len(existing_token) > 8 else existing_token
            print(f"   令牌预览: {token_preview}")
            
            print("🔄 模拟自动登录流程:")
            print("   1. 应用启动")
            print("   2. 检查配置文件")
            print("   3. 发现保存的令牌")
            print("   4. 验证令牌有效性")
            print("   5. 自动登录成功")
            
            # 这里可以添加实际的令牌验证逻辑
            print("💡 在真实环境中，会调用API验证令牌")
            
        else:
            print("ℹ️ 未发现现有令牌")
            print("🔄 模拟首次登录流程:")
            print("   1. 应用启动")
            print("   2. 检查配置文件")
            print("   3. 未发现令牌")
            print("   4. 显示登录对话框")
            print("   5. 用户输入令牌")
            print("   6. 保存令牌到配置文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 自动登录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 配置文件结构适配测试")
    print("=" * 50)
    
    # 测试加载现有配置
    loading_success = test_existing_config_loading()
    
    # 测试配置方法
    methods_success = test_config_methods()
    
    # 测试配置保存结构
    save_success = test_config_save_structure()
    
    # 测试自动登录
    auto_login_success = test_auto_login_with_existing_token()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 现有配置加载: {'✅ 通过' if loading_success else '❌ 失败'}")
    print(f"  - 配置方法测试: {'✅ 通过' if methods_success else '❌ 失败'}")
    print(f"  - 配置保存结构: {'✅ 通过' if save_success else '❌ 失败'}")
    print(f"  - 自动登录测试: {'✅ 通过' if auto_login_success else '❌ 失败'}")
    
    if all([loading_success, methods_success, save_success, auto_login_success]):
        print("\n🎉 所有测试通过！配置管理器已适配现有配置文件结构。")
        print("\n📁 配置文件结构:")
        print("   ├── API")
        print("   │   ├── base_url")
        print("   │   └── default_token")
        print("   ├── GUI")
        print("   │   ├── window_width")
        print("   │   ├── window_height")
        print("   │   ├── theme")
        print("   │   ├── last_selected_book")
        print("   │   └── remember_token")
        print("   └── Debug")
        print("       ├── enabled")
        print("       └── log_level")
        
        print("\n🚀 现在可以启动GUI测试自动登录:")
        print("   python run_gui.py")
        print("\n💡 自动登录行为:")
        print("   - 如果配置文件中有default_token，会尝试自动登录")
        print("   - 如果令牌验证失败，会清除令牌并显示登录对话框")
        print("   - 如果没有令牌，直接显示登录对话框")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查配置管理器实现。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
