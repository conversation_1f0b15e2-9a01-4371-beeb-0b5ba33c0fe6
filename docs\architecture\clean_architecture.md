# 新架构设计文档

## 概述

本项目采用清洁架构（Clean Architecture）和领域驱动设计（DDD）原则进行重构，实现了完全独立分离的模块化设计。

## 架构层次

### 1. 领域层 (Domain Layer)
- **实体 (Entities)**: Character, CV, Assignment
- **值对象 (Value Objects)**: CharacterID, CVID, MatchResult
- **领域服务 (Domain Services)**: CVMatcher
- **仓储接口 (Repository Interfaces)**: CharacterRepository, CVRepository等

### 2. 应用层 (Application Layer)
- **用例 (Use Cases)**: GetCharactersUseCase, AssignCVUseCase
- **应用服务 (Application Services)**: CharacterApplicationService
- **DTO (Data Transfer Objects)**: CharacterDTO, CVDTO等

### 3. 基础设施层 (Infrastructure Layer)
- **API客户端**: GStudiosAPIClient
- **仓储实现**: GStudiosCharacterRepository
- **配置管理**: JsonConfigRepository

### 4. 表示层 (Presentation Layer)
- **控制器 (Controllers)**: CharacterController
- **视图模型 (View Models)**: CharacterViewModel
- **视图 (Views)**: GUI界面

## 依赖方向

```
表示层 → 应用层 → 领域层 ← 基础设施层
```

## 设计原则

1. **依赖倒置**: 高层模块不依赖低层模块
2. **单一职责**: 每个类只有一个变化的理由
3. **开闭原则**: 对扩展开放，对修改关闭
4. **接口隔离**: 客户端不应依赖它不需要的接口

## 优势

- **可测试性**: 每层都可以独立测试
- **可维护性**: 清晰的职责分离
- **可扩展性**: 易于添加新功能
- **可移植性**: 业务逻辑与技术实现分离
