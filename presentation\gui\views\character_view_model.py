"""角色视图模型模块

此模块实现了角色相关的视图模型。
"""

from typing import List, Optional
from dataclasses import dataclass


@dataclass
class CharacterViewModel:
    """角色视图模型"""
    id: str
    name: str
    cv_name: Optional[str] = None
    is_assigned: bool = False
    display_text: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.display_text:
            cv_info = f" ({self.cv_name})" if self.cv_name else " (未分配)"
            self.display_text = f"{self.name}{cv_info}"


@dataclass
class CharacterListViewModel:
    """角色列表视图模型"""
    characters: List[CharacterViewModel]
    total_count: int
    assigned_count: int
    unassigned_count: int
    
    @classmethod
    def from_dto_list(cls, character_dtos):
        """从DTO列表创建视图模型"""
        view_models = []
        assigned_count = 0
        
        for dto in character_dtos:
            vm = CharacterViewModel(
                id=dto.id,
                name=dto.name,
                cv_name=dto.cv_id,
                is_assigned=dto.is_cv_assigned
            )
            view_models.append(vm)
            
            if dto.is_cv_assigned:
                assigned_count += 1
        
        return cls(
            characters=view_models,
            total_count=len(view_models),
            assigned_count=assigned_count,
            unassigned_count=len(view_models) - assigned_count
        )
