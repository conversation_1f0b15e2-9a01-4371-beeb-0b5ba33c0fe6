"""角色仓储接口模块

此模块定义了角色仓储的接口，用于管理角色数据的持久化操作。
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from domain.entities.character import Character
from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID


class CharacterRepository(ABC):
    """角色仓储接口
    
    定义了角色数据访问的抽象接口，具体实现在基础设施层。
    """
    
    @abstractmethod
    def get_by_id(self, character_id: CharacterID) -> Optional[Character]:
        """根据ID获取角色
        
        Args:
            character_id: 角色ID
            
        Returns:
            Optional[Character]: 角色实体，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_by_book_id(self, book_id: str) -> List[Character]:
        """根据书籍ID获取角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Character]: 角色列表
        """
        pass
    
    @abstractmethod
    def get_by_name(self, name: str, book_id: str) -> Optional[Character]:
        """根据名称和书籍ID获取角色
        
        Args:
            name: 角色名称
            book_id: 书籍ID
            
        Returns:
            Optional[Character]: 角色实体，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def get_unassigned_characters(self, book_id: str) -> List[Character]:
        """获取未分配CV的角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Character]: 未分配CV的角色列表
        """
        pass
    
    @abstractmethod
    def get_assigned_characters(self, book_id: str) -> List[Character]:
        """获取已分配CV的角色列表
        
        Args:
            book_id: 书籍ID
            
        Returns:
            List[Character]: 已分配CV的角色列表
        """
        pass
    
    @abstractmethod
    def save(self, character: Character) -> None:
        """保存角色
        
        Args:
            character: 要保存的角色实体
        """
        pass
    
    @abstractmethod
    def save_all(self, characters: List[Character]) -> None:
        """批量保存角色
        
        Args:
            characters: 要保存的角色列表
        """
        pass
    
    @abstractmethod
    def update_cv_assignment(self, character_id: CharacterID, cv_id: CVID) -> bool:
        """更新角色的CV分配
        
        Args:
            character_id: 角色ID
            cv_id: CV ID
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    def remove_cv_assignment(self, character_id: CharacterID) -> bool:
        """移除角色的CV分配
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 是否移除成功
        """
        pass
    
    @abstractmethod
    def exists(self, character_id: CharacterID) -> bool:
        """检查角色是否存在
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 是否存在
        """
        pass
    
    @abstractmethod
    def count_by_book_id(self, book_id: str) -> int:
        """统计指定书籍的角色数量
        
        Args:
            book_id: 书籍ID
            
        Returns:
            int: 角色数量
        """
        pass
    
    @abstractmethod
    def find_by_cv_id(self, cv_id: CVID, book_id: str) -> List[Character]:
        """根据CV ID查找角色
        
        Args:
            cv_id: CV ID
            book_id: 书籍ID
            
        Returns:
            List[Character]: 使用该CV的角色列表
        """
        pass
