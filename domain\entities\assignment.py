"""分配实体模块

此模块定义了分配(Assignment)实体，表示角色与CV的分配关系。
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from enum import Enum

from domain.value_objects.character_id import CharacterID
from domain.value_objects.cv_id import CVID
from domain.exceptions.assignment_exceptions import InvalidAssignmentError


class AssignmentStatus(Enum):
    """分配状态枚举"""
    PENDING = "pending"      # 待处理
    ASSIGNED = "assigned"    # 已分配
    FAILED = "failed"        # 分配失败
    CANCELLED = "cancelled"  # 已取消


class MatchType(Enum):
    """匹配类型枚举"""
    DIRECT = "direct"        # 直接匹配
    NICKNAME = "nickname"    # 简名匹配
    MANUAL = "manual"        # 手动分配
    NONE = "none"           # 未匹配


@dataclass
class Assignment:
    """分配实体
    
    表示角色与CV之间的分配关系，包含分配的详细信息和状态。
    
    Attributes:
        character_id: 角色标识符
        cv_id: CV标识符，可选
        status: 分配状态
        match_type: 匹配类型
        created_at: 创建时间
        updated_at: 更新时间
        error_message: 错误信息，可选
        match_info: 匹配信息，可选（如简名匹配的详情）
    """
    
    character_id: CharacterID
    cv_id: Optional[CVID] = None
    status: AssignmentStatus = AssignmentStatus.PENDING
    match_type: MatchType = MatchType.NONE
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    error_message: Optional[str] = None
    match_info: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            object.__setattr__(self, 'created_at', datetime.now())
        if self.updated_at is None:
            object.__setattr__(self, 'updated_at', self.created_at)
        
        self._validate()
    
    def _validate(self) -> None:
        """验证分配数据的有效性"""
        if self.character_id is None:
            raise InvalidAssignmentError("角色ID不能为空")
        
        # 如果状态是已分配，必须有CV ID
        if self.status == AssignmentStatus.ASSIGNED and self.cv_id is None:
            raise InvalidAssignmentError("已分配状态必须有CV ID")
    
    def assign_cv(self, cv_id: CVID, match_type: MatchType = MatchType.MANUAL, 
                  match_info: Optional[str] = None) -> None:
        """分配CV
        
        Args:
            cv_id: CV标识符
            match_type: 匹配类型
            match_info: 匹配信息
            
        Raises:
            InvalidAssignmentError: 当CV ID无效时抛出
        """
        if cv_id is None:
            raise InvalidAssignmentError("CV ID不能为空")
        
        object.__setattr__(self, 'cv_id', cv_id)
        object.__setattr__(self, 'status', AssignmentStatus.ASSIGNED)
        object.__setattr__(self, 'match_type', match_type)
        object.__setattr__(self, 'match_info', match_info)
        object.__setattr__(self, 'error_message', None)
        object.__setattr__(self, 'updated_at', datetime.now())
    
    def mark_as_failed(self, error_message: str) -> None:
        """标记为失败
        
        Args:
            error_message: 错误信息
        """
        if not error_message:
            raise InvalidAssignmentError("错误信息不能为空")
        
        object.__setattr__(self, 'status', AssignmentStatus.FAILED)
        object.__setattr__(self, 'error_message', error_message)
        object.__setattr__(self, 'updated_at', datetime.now())
    
    def cancel(self) -> None:
        """取消分配"""
        object.__setattr__(self, 'status', AssignmentStatus.CANCELLED)
        object.__setattr__(self, 'cv_id', None)
        object.__setattr__(self, 'match_type', MatchType.NONE)
        object.__setattr__(self, 'match_info', None)
        object.__setattr__(self, 'updated_at', datetime.now())
    
    def retry(self) -> None:
        """重试分配"""
        object.__setattr__(self, 'status', AssignmentStatus.PENDING)
        object.__setattr__(self, 'error_message', None)
        object.__setattr__(self, 'updated_at', datetime.now())
    
    def is_successful(self) -> bool:
        """检查是否分配成功"""
        return self.status == AssignmentStatus.ASSIGNED and self.cv_id is not None
    
    def is_failed(self) -> bool:
        """检查是否分配失败"""
        return self.status == AssignmentStatus.FAILED
    
    def is_pending(self) -> bool:
        """检查是否待处理"""
        return self.status == AssignmentStatus.PENDING
    
    def is_cancelled(self) -> bool:
        """检查是否已取消"""
        return self.status == AssignmentStatus.CANCELLED
    
    def is_nickname_match(self) -> bool:
        """检查是否是简名匹配"""
        return self.match_type == MatchType.NICKNAME
    
    def is_direct_match(self) -> bool:
        """检查是否是直接匹配"""
        return self.match_type == MatchType.DIRECT
    
    def is_manual_assignment(self) -> bool:
        """检查是否是手动分配"""
        return self.match_type == MatchType.MANUAL
    
    def get_status_display(self) -> str:
        """获取状态的显示文本"""
        status_map = {
            AssignmentStatus.PENDING: "待处理",
            AssignmentStatus.ASSIGNED: "已分配",
            AssignmentStatus.FAILED: "失败",
            AssignmentStatus.CANCELLED: "已取消"
        }
        return status_map.get(self.status, "未知")
    
    def get_match_type_display(self) -> str:
        """获取匹配类型的显示文本"""
        type_map = {
            MatchType.DIRECT: "直接匹配",
            MatchType.NICKNAME: "简名匹配",
            MatchType.MANUAL: "手动分配",
            MatchType.NONE: "未匹配"
        }
        return type_map.get(self.match_type, "未知")
    
    def __str__(self) -> str:
        """字符串表示"""
        cv_info = f" -> CV:{self.cv_id}" if self.cv_id else ""
        return f"分配[角色:{self.character_id}{cv_info}] ({self.get_status_display()})"
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return (f"Assignment(character_id={self.character_id!r}, "
                f"cv_id={self.cv_id!r}, status={self.status!r}, "
                f"match_type={self.match_type!r})")
    
    def __eq__(self, other) -> bool:
        """相等性比较 - 基于角色ID"""
        if not isinstance(other, Assignment):
            return False
        return self.character_id == other.character_id
    
    def __hash__(self) -> int:
        """哈希值 - 基于角色ID"""
        return hash(self.character_id)
