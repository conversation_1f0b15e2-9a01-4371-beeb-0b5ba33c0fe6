"""性能测试模块

此模块包含系统性能测试。
"""

import time
import pytest
from unittest.mock import Mock
from domain.services.cv_matcher import CVMatcher
from domain.entities.cv import CV
from domain.value_objects.cv_id import CVID


class TestPerformance:
    """性能测试类"""
    
    def test_cv_matching_performance(self):
        """测试CV匹配性能"""
        # 创建大量测试数据
        cvs = []
        for i in range(1000):
            cv = CV(
                id=CVID.from_string(str(i)),
                name=f"CV_{i}",
                book_id="test_book"
            )
            cvs.append(cv)
        
        # 创建匹配器
        nickname_repo = Mock()
        nickname_repo.get_full_name.return_value = "CV_500"
        matcher = CVMatcher(nickname_repo)
        
        # 测试匹配性能
        start_time = time.time()
        
        for i in range(100):
            result = matcher.match_cv("CV_500", cvs)
            assert result.is_successful()
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # 100次匹配应该在1秒内完成
        assert elapsed < 1.0, f"匹配耗时过长: {elapsed}秒"
    
    def test_batch_processing_performance(self):
        """测试批量处理性能"""
        # 创建测试数据
        cv_names = [f"CV_{i}" for i in range(100)]
        cvs = [
            CV(id=CVID.from_string(str(i)), name=name, book_id="test")
            for i, name in enumerate(cv_names)
        ]
        
        nickname_repo = Mock()
        nickname_repo.get_full_name.side_effect = lambda x: x
        matcher = CVMatcher(nickname_repo)
        
        # 测试批量匹配性能
        start_time = time.time()
        results = matcher.batch_match_cvs(cv_names, cvs)
        end_time = time.time()
        
        elapsed = end_time - start_time
        
        # 批量处理应该很快
        assert elapsed < 0.5, f"批量处理耗时过长: {elapsed}秒"
        assert len(results) == 100
