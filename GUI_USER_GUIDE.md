# CV分配工具 GUI 使用指南

## 🚀 启动应用

### 启动命令
```bash
python run_gui.py
```

## 🔐 登录认证

### 首次启动
1. 应用启动后会自动显示登录对话框
2. 您有两种选择：
   - **输入API令牌**: 连接真实的GStudios API
   - **使用模拟数据**: 无需令牌，使用测试数据

### API令牌登录
1. 在"API令牌"输入框中输入您的GStudios API令牌
2. 可以勾选"显示令牌"来查看输入内容
3. 点击"登录"按钮
4. 成功后窗口标题会显示"(已连接)"

### 模拟数据模式
1. 点击"使用模拟数据"按钮
2. 确认使用模拟数据
3. 窗口标题会显示"(模拟数据)"
4. 系统会加载预设的测试书籍和角色

### 重新登录
- 通过菜单栏 "文件" → "重新登录" 可以重新进行认证
- 快捷键: `Ctrl+L`

## 📚 使用流程

### 1. 选择书籍
- 登录成功后，系统会自动加载书籍列表
- 在控制面板的下拉框中选择要操作的书籍
- 书籍显示格式: "书籍名称 (状态)"

### 2. 加载角色
- 选择书籍后，点击"加载角色"按钮
- 系统会在后台加载该书籍的角色数据
- 加载过程中会显示进度条

### 3. 查看角色信息
- 角色列表显示在表格中，包含：
  - 角色名称
  - 角色ID
  - 当前CV (显示CV名字，如"张小雨"、"李明轩")
  - 状态 (已分配/未分配)
- 已分配CV的角色显示为绿色背景
- 未分配CV的角色显示为黄色背景
- **CV显示优先级**：
  1. 优先显示CV名字（如：张小雨）
  2. 如果没有名字则显示CV ID（如：cv_001）
  3. 未分配显示"未分配"

### 4. 分配CV
- 在角色列表中选择一个角色
- 右侧面板会显示选中的角色信息
- 在CV下拉框中选择要分配的CV
- 点击"分配CV"按钮
- **注意**: 当前版本显示功能提示，实际分配功能正在开发中

### 5. 取消分配
- 选择已分配CV的角色
- 点击"取消分配"按钮
- 确认操作
- **注意**: 当前版本显示功能提示，实际功能正在开发中

## 🎛️ 界面功能

### 菜单栏
- **文件菜单**:
  - 重新登录 (`Ctrl+L`): 重新进行API认证
  - 退出 (`Ctrl+Q`): 关闭应用程序
- **帮助菜单**:
  - 关于: 显示应用程序信息

### 状态栏
- 显示当前操作状态
- 连接状态提示
- 数据加载进度

### 控制面板
- 书籍选择下拉框
- 加载角色按钮
- 进度条 (加载时显示)

### 角色列表
- 表格形式显示角色信息
- 支持行选择
- 颜色编码状态显示
- 自动调整列宽

### CV分配面板
- 显示选中角色信息
- CV选择下拉框
- 分配/取消分配按钮

## ⚠️ 注意事项

### 模拟数据模式
- 包含3本测试书籍
- 每本书籍包含5个测试角色
- 部分角色预设了CV分配状态
- 所有操作仅在内存中进行，不会保存

### API令牌模式
- 需要有效的GStudios API令牌
- 令牌会自动保存到配置文件
- 支持真实的书籍和角色数据
- 网络连接异常时会显示错误信息

### 性能优化
- 数据加载使用后台线程，不会阻塞界面
- 大量数据时会显示进度提示
- 支持取消正在进行的操作

## 🔧 故障排除

### 登录失败
- 检查API令牌是否正确
- 确认网络连接正常
- 尝试使用模拟数据模式测试界面

### 数据加载失败
- 检查选择的书籍是否有效
- 确认API令牌权限
- 查看状态栏的错误信息

### 界面异常
- 尝试重新登录
- 重启应用程序
- 检查Python和PyQt5版本

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本 (需要3.8+)
2. PyQt5安装状态
3. 网络连接
4. API令牌有效性

---
*最后更新: 2025-06-18*
*版本: 2.0.0*
