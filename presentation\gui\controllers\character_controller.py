"""角色控制器模块

此模块实现了角色相关的控制器逻辑。
"""

from .base_controller import BaseController
from application.character_application_service import CharacterApplicationService


class CharacterController(BaseController):
    """角色控制器"""
    
    def __init__(self):
        """初始化角色控制器"""
        super().__init__()
        self._character_service = self.get_service(CharacterApplicationService)
    
    def get_characters(self, book_id: str):
        """获取角色列表"""
        return self._character_service.get_characters(book_id)
    
    def assign_cv(self, character_id: str, cv_id: str, book_id: str):
        """分配CV"""
        return self._character_service.assign_cv(character_id, cv_id, book_id)
    
    def get_unassigned_characters(self, book_id: str):
        """获取未分配CV的角色"""
        return self._character_service.get_unassigned_characters(book_id)
