"""匹配结果值对象模块"""

from dataclasses import dataclass
from typing import Optional
from enum import Enum

from .cv_id import CVID


class MatchType(Enum):
    """匹配类型枚举"""
    DIRECT = "direct"        # 直接匹配
    NICKNAME = "nickname"    # 简名匹配
    NONE = "none"           # 未匹配


@dataclass(frozen=True)
class MatchResult:
    """匹配结果值对象
    
    表示CV匹配的结果，包含匹配到的CV ID、匹配类型和相关信息。
    
    Attributes:
        cv_id: 匹配到的CV ID，如果未匹配则为None
        match_type: 匹配类型
        message: 匹配信息或错误信息
        confidence: 匹配置信度 (0.0-1.0)
    """
    
    cv_id: Optional[CVID]
    match_type: MatchType
    message: Optional[str] = None
    confidence: float = 1.0
    
    def __post_init__(self):
        """初始化后验证"""
        # 验证置信度范围
        if not (0.0 <= self.confidence <= 1.0):
            raise ValueError("匹配置信度必须在0.0到1.0之间")
        
        # 验证匹配逻辑的一致性
        if self.cv_id is not None and self.match_type == MatchType.NONE:
            raise ValueError("有CV ID时匹配类型不能为NONE")
        
        if self.cv_id is None and self.match_type != MatchType.NONE:
            raise ValueError("无CV ID时匹配类型必须为NONE")
    
    @classmethod
    def success(cls, cv_id: CVID, match_type: MatchType, 
                message: Optional[str] = None, confidence: float = 1.0) -> 'MatchResult':
        """创建成功匹配结果
        
        Args:
            cv_id: 匹配到的CV ID
            match_type: 匹配类型
            message: 匹配信息
            confidence: 匹配置信度
            
        Returns:
            MatchResult: 成功的匹配结果
        """
        return cls(cv_id=cv_id, match_type=match_type, 
                  message=message, confidence=confidence)
    
    @classmethod
    def direct_match(cls, cv_id: CVID, confidence: float = 1.0) -> 'MatchResult':
        """创建直接匹配结果
        
        Args:
            cv_id: 匹配到的CV ID
            confidence: 匹配置信度
            
        Returns:
            MatchResult: 直接匹配结果
        """
        return cls(cv_id=cv_id, match_type=MatchType.DIRECT, 
                  message="直接匹配", confidence=confidence)
    
    @classmethod
    def nickname_match(cls, cv_id: CVID, nickname: str, full_name: str, 
                      confidence: float = 0.9) -> 'MatchResult':
        """创建简名匹配结果
        
        Args:
            cv_id: 匹配到的CV ID
            nickname: 使用的简名
            full_name: 对应的全名
            confidence: 匹配置信度
            
        Returns:
            MatchResult: 简名匹配结果
        """
        message = f"{nickname} -> {full_name}"
        return cls(cv_id=cv_id, match_type=MatchType.NICKNAME, 
                  message=message, confidence=confidence)
    
    @classmethod
    def no_match(cls, message: Optional[str] = None) -> 'MatchResult':
        """创建无匹配结果
        
        Args:
            message: 无匹配的原因
            
        Returns:
            MatchResult: 无匹配结果
        """
        return cls(cv_id=None, match_type=MatchType.NONE, 
                  message=message or "未找到匹配的CV", confidence=0.0)
    
    @classmethod
    def unassigned(cls) -> 'MatchResult':
        """创建未分配结果（对应"—"符号）
        
        Returns:
            MatchResult: 未分配结果
        """
        return cls(cv_id=None, match_type=MatchType.NONE, 
                  message="未分配CV", confidence=1.0)
    
    def is_successful(self) -> bool:
        """检查是否匹配成功
        
        Returns:
            bool: 是否匹配成功
        """
        return self.cv_id is not None
    
    def is_direct_match(self) -> bool:
        """检查是否是直接匹配
        
        Returns:
            bool: 是否是直接匹配
        """
        return self.match_type == MatchType.DIRECT
    
    def is_nickname_match(self) -> bool:
        """检查是否是简名匹配
        
        Returns:
            bool: 是否是简名匹配
        """
        return self.match_type == MatchType.NICKNAME
    
    def is_no_match(self) -> bool:
        """检查是否无匹配
        
        Returns:
            bool: 是否无匹配
        """
        return self.match_type == MatchType.NONE
    
    def is_high_confidence(self, threshold: float = 0.8) -> bool:
        """检查是否是高置信度匹配
        
        Args:
            threshold: 置信度阈值
            
        Returns:
            bool: 是否是高置信度匹配
        """
        return self.confidence >= threshold
    
    def get_display_message(self) -> str:
        """获取显示消息
        
        Returns:
            str: 用于显示的消息
        """
        if self.message:
            return self.message
        
        if self.is_successful():
            return f"匹配成功 (CV: {self.cv_id})"
        else:
            return "未找到匹配的CV"
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.is_successful():
            return f"匹配成功: {self.cv_id} ({self.match_type.value})"
        else:
            return f"匹配失败: {self.message or '未知原因'}"
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return (f"MatchResult(cv_id={self.cv_id!r}, "
                f"match_type={self.match_type!r}, "
                f"message={self.message!r}, "
                f"confidence={self.confidence})")
    
    def __bool__(self) -> bool:
        """布尔值表示 - 是否匹配成功"""
        return self.is_successful()
