"""API令牌验证模块

此模块负责验证API令牌的有效性。
"""

from typing import Tuple, Optional
import requests
from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig


class TokenValidator:
    """API令牌验证器"""
    
    @staticmethod
    def validate_token(token: str, base_url: str = "https://www.gstudios.com.cn/story_v2/api") -> Tuple[bool, str]:
        """验证API令牌
        
        Args:
            token: API令牌
            base_url: API基础URL
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误消息或成功消息)
        """
        if not token or not token.strip():
            return False, "令牌不能为空"
        
        try:
            # 创建API客户端
            config = APIConfig(base_url, token.strip())
            client = GStudiosAPIClient(config)
            
            # 尝试获取书籍列表来验证令牌
            success, books = client.get_books("all")
            
            if success:
                return True, f"令牌验证成功，获取到 {len(books)} 本书籍"
            else:
                return False, "令牌验证失败：无法获取书籍列表"
                
        except requests.exceptions.ConnectionError:
            return False, "网络连接失败，请检查网络连接"
        except requests.exceptions.Timeout:
            return False, "请求超时，请稍后重试"
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                return False, "令牌无效或已过期"
            elif e.response.status_code == 403:
                return False, "令牌权限不足"
            else:
                return False, f"HTTP错误: {e.response.status_code}"
        except Exception as e:
            return False, f"验证过程中发生错误: {str(e)}"
    
    @staticmethod
    def quick_validate_token(token: str, base_url: str = "https://www.gstudios.com.cn/story_v2/api") -> bool:
        """快速验证API令牌（只返回是否有效）
        
        Args:
            token: API令牌
            base_url: API基础URL
            
        Returns:
            bool: 令牌是否有效
        """
        is_valid, _ = TokenValidator.validate_token(token, base_url)
        return is_valid
    
    @staticmethod
    def validate_token_with_retry(token: str, base_url: str = "https://www.gstudios.com.cn/story_v2/api", 
                                 max_retries: int = 3) -> Tuple[bool, str]:
        """带重试的令牌验证
        
        Args:
            token: API令牌
            base_url: API基础URL
            max_retries: 最大重试次数
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误消息或成功消息)
        """
        last_error = ""
        
        for attempt in range(max_retries):
            try:
                is_valid, message = TokenValidator.validate_token(token, base_url)
                if is_valid:
                    return True, message
                
                # 如果是网络相关错误，可以重试
                if "网络连接失败" in message or "请求超时" in message:
                    last_error = message
                    continue
                else:
                    # 其他错误（如令牌无效）不需要重试
                    return False, message
                    
            except Exception as e:
                last_error = f"验证过程中发生错误: {str(e)}"
                continue
        
        return False, f"重试 {max_retries} 次后仍然失败: {last_error}"


# 便捷函数
def validate_api_token(token: str, base_url: str = "https://www.gstudios.com.cn/story_v2/api") -> Tuple[bool, str]:
    """验证API令牌（便捷函数）"""
    return TokenValidator.validate_token(token, base_url)


def is_token_valid(token: str, base_url: str = "https://www.gstudios.com.cn/story_v2/api") -> bool:
    """检查令牌是否有效（便捷函数）"""
    return TokenValidator.quick_validate_token(token, base_url)
