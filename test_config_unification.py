#!/usr/bin/env python3
"""
配置文件位置统一测试脚本

验证所有配置访问代码都使用相同的配置文件位置
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_config_manager_location():
    """测试新配置管理器的位置"""
    print("🔍 测试新配置管理器位置...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        config_path = config_manager.get_config_file_path()
        
        print(f"📁 新配置管理器路径: {config_path}")
        
        # 检查是否在config目录下
        expected_path = project_root / 'config' / 'config.json'
        is_correct = Path(config_path) == expected_path
        
        print(f"✅ 路径正确: {'是' if is_correct else '否'}")
        print(f"   期望路径: {expected_path}")
        print(f"   实际路径: {config_path}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 新配置管理器测试失败: {e}")
        return False

def test_json_config_repository_location():
    """测试JSON配置仓储的位置"""
    print("\n🔍 测试JSON配置仓储位置...")
    
    try:
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        # 创建默认配置仓储
        config_repo = JsonConfigRepository()
        config_path = config_repo._config_file
        
        print(f"📁 JSON配置仓储路径: {config_path}")
        
        # 检查是否在config目录下
        expected_path = project_root / 'config' / 'config.json'
        is_correct = Path(config_path) == expected_path
        
        print(f"✅ 路径正确: {'是' if is_correct else '否'}")
        print(f"   期望路径: {expected_path}")
        print(f"   实际路径: {config_path}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ JSON配置仓储测试失败: {e}")
        return False

def test_container_config_location():
    """测试依赖注入容器的配置位置"""
    print("\n🔍 测试依赖注入容器配置位置...")
    
    try:
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        # 通过容器获取配置仓储
        config_repo = container.get(JsonConfigRepository)
        config_path = config_repo._config_file
        
        print(f"📁 容器配置仓储路径: {config_path}")
        
        # 检查是否在config目录下
        expected_path = project_root / 'config' / 'config.json'
        is_correct = Path(config_path) == expected_path
        
        print(f"✅ 路径正确: {'是' if is_correct else '否'}")
        print(f"   期望路径: {expected_path}")
        print(f"   实际路径: {config_path}")
        
        return is_correct
        
    except Exception as e:
        print(f"❌ 容器配置测试失败: {e}")
        return False

def test_config_data_consistency():
    """测试配置数据一致性"""
    print("\n🔍 测试配置数据一致性...")
    
    try:
        from utils.config_manager import get_config_manager
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        
        # 通过新配置管理器读取
        config_manager = get_config_manager()
        new_token = config_manager.get_api_token()
        new_base_url = config_manager.get_api_base_url()
        
        # 通过旧配置仓储读取
        config_repo = container.get(JsonConfigRepository)
        old_token = config_repo.get('API', 'default_token')
        old_base_url = config_repo.get('API', 'base_url')
        
        print(f"📋 配置数据对比:")
        print(f"   新管理器令牌: {new_token[:8] + '...' if new_token else 'None'}")
        print(f"   旧仓储令牌: {old_token[:8] + '...' if old_token else 'None'}")
        print(f"   新管理器URL: {new_base_url}")
        print(f"   旧仓储URL: {old_base_url}")
        
        # 检查数据一致性
        token_consistent = new_token == old_token
        url_consistent = new_base_url == old_base_url
        
        print(f"✅ 令牌一致: {'是' if token_consistent else '否'}")
        print(f"✅ URL一致: {'是' if url_consistent else '否'}")
        
        return token_consistent and url_consistent
        
    except Exception as e:
        print(f"❌ 配置数据一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_write_consistency():
    """测试配置写入一致性"""
    print("\n🔍 测试配置写入一致性...")
    
    try:
        from utils.config_manager import get_config_manager
        from app.container import container
        from infrastructure.persistence.json_config_repository import JsonConfigRepository
        import time
        
        # 备份原始配置
        config_manager = get_config_manager()
        original_config = config_manager.load_config()
        
        # 通过新配置管理器写入测试数据
        test_token = f"test_token_{int(time.time())}"
        config_manager.set_api_token(test_token, remember=True)
        
        # 通过旧配置仓储读取
        config_repo = container.get(JsonConfigRepository)
        config_repo.reload()  # 重新加载文件
        read_token = config_repo.get('API', 'default_token')
        
        print(f"📝 写入测试:")
        print(f"   写入令牌: {test_token}")
        print(f"   读取令牌: {read_token}")
        
        # 检查一致性
        is_consistent = test_token == read_token
        print(f"✅ 写入读取一致: {'是' if is_consistent else '否'}")
        
        # 恢复原始配置
        config_manager.save_config(original_config)
        
        return is_consistent
        
    except Exception as e:
        print(f"❌ 配置写入一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_existence():
    """测试配置文件存在性"""
    print("\n🔍 测试配置文件存在性...")
    
    config_files = [
        project_root / 'config' / 'config.json',
        project_root / 'config.json',
        project_root / 'config1.json'
    ]
    
    print("📄 配置文件检查:")
    for config_file in config_files:
        exists = config_file.exists()
        size = config_file.stat().st_size if exists else 0
        print(f"   {config_file.name}: {'✅ 存在' if exists else '❌ 不存在'} ({size} bytes)")
    
    # 主配置文件应该存在
    main_config = project_root / 'config' / 'config.json'
    return main_config.exists()

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 配置文件位置统一测试")
    print("=" * 50)
    
    # 测试各组件的配置位置
    new_manager_success = test_config_manager_location()
    json_repo_success = test_json_config_repository_location()
    container_success = test_container_config_location()
    
    # 测试配置数据一致性
    data_consistency = test_config_data_consistency()
    write_consistency = test_config_write_consistency()
    
    # 测试文件存在性
    file_existence = test_file_existence()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 新配置管理器位置: {'✅ 正确' if new_manager_success else '❌ 错误'}")
    print(f"  - JSON配置仓储位置: {'✅ 正确' if json_repo_success else '❌ 错误'}")
    print(f"  - 容器配置位置: {'✅ 正确' if container_success else '❌ 错误'}")
    print(f"  - 配置数据一致性: {'✅ 一致' if data_consistency else '❌ 不一致'}")
    print(f"  - 配置写入一致性: {'✅ 一致' if write_consistency else '❌ 不一致'}")
    print(f"  - 配置文件存在: {'✅ 存在' if file_existence else '❌ 不存在'}")
    
    all_success = all([
        new_manager_success, json_repo_success, container_success,
        data_consistency, write_consistency, file_existence
    ])
    
    if all_success:
        print("\n🎉 所有测试通过！配置文件位置已统一。")
        print("\n📁 统一的配置文件位置:")
        print(f"   {project_root / 'config' / 'config.json'}")
        print("\n✅ 所有组件现在都使用相同的配置文件:")
        print("   - 新配置管理器 (utils/config_manager.py)")
        print("   - JSON配置仓储 (infrastructure/persistence/json_config_repository.py)")
        print("   - 依赖注入容器 (app/container.py)")
        print("   - GUI主窗口 (presentation/gui/main_window.py)")
        
        print("\n🚀 现在可以安全启动GUI:")
        print("   python run_gui.py")
        return 0
    else:
        print("\n⚠️ 部分测试失败，配置文件位置可能不统一。")
        print("💡 建议检查:")
        print("   1. 确保config目录存在")
        print("   2. 检查config/config.json文件")
        print("   3. 验证所有组件使用相同路径")
        return 1

if __name__ == "__main__":
    sys.exit(main())
