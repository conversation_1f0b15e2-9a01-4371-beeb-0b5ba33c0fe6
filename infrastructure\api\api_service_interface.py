"""API服务接口模块

此模块定义了API服务的抽象接口，用于解耦业务逻辑与具体的API实现。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Any


class APIServiceInterface(ABC):
    """API服务接口
    
    定义了与外部API交互的抽象接口，具体实现在基础设施层。
    """
    
    @abstractmethod
    def get_books(self, finished: str = "all") -> Tuple[bool, List[Dict[str, Any]]]:
        """获取书籍列表
        
        Args:
            finished: 书籍完成状态
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 书籍列表)
        """
        pass
    
    @abstractmethod
    def get_characters(self, book_id: str, **filters) -> Tuple[bool, List[Dict[str, Any]]]:
        """获取角色列表
        
        Args:
            book_id: 书籍ID
            **filters: 筛选参数
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, 角色列表)
        """
        pass
    
    @abstractmethod
    def get_cvs(self, book_id: str, cv_type: str = "human") -> Tu<PERSON>[bool, List[Dict[str, Any]]]:
        """获取CV列表
        
        Args:
            book_id: 书籍ID
            cv_type: CV类型
        
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (成功标志, CV列表)
        """
        pass
    
    @abstractmethod
    def assign_cv_to_character(self, character_id: str, cv_id: str) -> Tuple[bool, str]:
        """分配CV给角色
        
        Args:
            character_id: 角色ID
            cv_id: CV ID
        
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        pass
    
    @abstractmethod
    def set_token(self, token: str) -> None:
        """设置认证令牌
        
        Args:
            token: 认证令牌
        """
        pass
