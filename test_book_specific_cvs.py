#!/usr/bin/env python3
"""
书籍特定CV测试脚本

测试不同书籍返回不同CV列表的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_book_specific_cvs():
    """测试书籍特定的CV列表"""
    try:
        print("🔍 测试书籍特定CV列表功能...")
        from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
        
        # 创建API客户端（无令牌，使用模拟数据）
        config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
        client = GStudiosAPIClient(config)
        print("✅ API客户端创建成功")
        
        # 测试不同书籍的CV列表
        test_books = [
            ("1", "测试书籍1 - 现代都市小说"),
            ("2", "测试书籍2 - 古风仙侠小说"),
            ("3", "示例小说 - 家庭温情小说"),
            ("999", "不存在的书籍 - 应返回默认列表")
        ]
        
        print("\n📚 不同书籍的CV列表:")
        print("=" * 80)
        
        for book_id, book_name in test_books:
            print(f"\n📖 {book_name} (ID: {book_id})")
            print("-" * 60)
            
            success, cvs = client.get_cvs(book_id, "human")
            
            if success:
                print(f"✅ 获取到 {len(cvs)} 个CV:")
                for i, cv in enumerate(cvs, 1):
                    name = cv.get('name', 'Unknown')
                    gender = cv.get('gender', 'Unknown')
                    age = cv.get('age', 'Unknown')
                    description = cv.get('description', 'No description')
                    print(f"  {i}. {name} ({gender}, {age}) - {description}")
            else:
                print("❌ 获取CV列表失败")
        
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 书籍特定CV测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cv_assignment_relevance():
    """测试CV分配的相关性"""
    print("\n🔍 测试CV分配的相关性...")
    
    print("📋 书籍CV分配策略:")
    print("=" * 50)
    
    strategies = [
        {
            "book": "现代都市小说",
            "cvs": ["张小雨(温柔甜美)", "李明轩(阳光帅气)", "王诗涵(清纯可爱)", "陈浩然(成熟稳重)"],
            "reason": "适合现代都市背景，声音风格现代化"
        },
        {
            "book": "古风仙侠小说", 
            "cvs": ["刘雅琪(知性优雅)", "赵子轩(活泼开朗)", "林若仙(空灵仙气)", "萧逸风(潇洒不羁)"],
            "reason": "适合古风仙侠背景，声音风格古典雅致"
        },
        {
            "book": "家庭温情小说",
            "cvs": ["孙美琳(慈祥温和)", "周志强(威严厚重)", "温暖妈妈(温暖慈爱)", "和蔼爸爸(和蔼可亲)"],
            "reason": "适合家庭题材，声音风格温暖亲切"
        }
    ]
    
    for strategy in strategies:
        print(f"\n📖 {strategy['book']}:")
        print(f"   💡 策略: {strategy['reason']}")
        print(f"   🎤 可用CV:")
        for cv in strategy['cvs']:
            print(f"      - {cv}")
    
    print("\n✨ 优势:")
    print("  - 每本书籍都有专门匹配的CV")
    print("  - CV风格与书籍题材相符")
    print("  - 避免不合适的CV出现在选择列表中")
    print("  - 提升分配的准确性和合理性")
    
    return True

def test_gui_workflow():
    """测试GUI工作流程"""
    print("\n🔍 测试GUI工作流程...")
    
    print("📋 完整的CV分配工作流程:")
    print("=" * 50)
    
    workflow_steps = [
        "1. 用户选择书籍分类（全部/进行中/已完成）",
        "2. 从过滤后的书籍列表中选择目标书籍",
        "3. 点击'加载角色'按钮",
        "4. 系统并行加载:",
        "   ├─ 该书籍的所有角色",
        "   └─ 该书籍已加入的所有CV",
        "5. 角色表格显示角色及其当前CV状态",
        "6. CV下拉框显示该书籍可用的CV选项",
        "7. 用户选择角色，然后从该书籍的CV中选择",
        "8. 执行CV分配操作"
    ]
    
    for step in workflow_steps:
        print(f"  {step}")
    
    print(f"\n🎯 关键改进:")
    print("  - CV列表现在是书籍特定的")
    print("  - 不会显示其他书籍的CV")
    print("  - 分配更加精确和相关")
    print("  - 避免跨书籍的CV混乱")
    
    return True

def test_api_service_integration():
    """测试API服务集成"""
    print("\n🔍 测试API服务集成...")
    
    try:
        from app.container import container
        from application.book_application_service import BookApplicationService
        from infrastructure.api.gstudios_api_service import GStudiosAPIService
        
        # 获取书籍服务
        book_service = container.get(BookApplicationService)
        api_service = container.get(GStudiosAPIService)
        
        print("✅ 服务获取成功")
        
        # 获取书籍列表
        books_response = book_service.get_books("all")
        if books_response.success:
            print(f"📚 获取到 {len(books_response.books)} 本书籍")
            
            # 测试每本书的CV列表
            print("\n📋 每本书的CV数量:")
            for book in books_response.books:
                success, cvs = api_service.get_cvs(book.id, "human")
                if success:
                    print(f"  - {book.name}: {len(cvs)} 个CV")
                else:
                    print(f"  - {book.name}: CV获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ API服务集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - 书籍特定CV测试")
    print("=" * 50)
    
    # 测试书籍特定CV
    book_cvs_success = test_book_specific_cvs()
    
    # 测试CV分配相关性
    relevance_success = test_cv_assignment_relevance()
    
    # 测试GUI工作流程
    workflow_success = test_gui_workflow()
    
    # 测试API服务集成
    integration_success = test_api_service_integration()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 书籍特定CV: {'✅ 通过' if book_cvs_success else '❌ 失败'}")
    print(f"  - CV分配相关性: {'✅ 通过' if relevance_success else '❌ 失败'}")
    print(f"  - GUI工作流程: {'✅ 通过' if workflow_success else '❌ 失败'}")
    print(f"  - API服务集成: {'✅ 通过' if integration_success else '❌ 失败'}")
    
    if all([book_cvs_success, relevance_success, workflow_success, integration_success]):
        print("\n🎉 所有测试通过！书籍特定CV功能正常。")
        print("\n🚀 启动GUI验证:")
        print("   python run_gui.py")
        print("\n💡 测试步骤:")
        print("   1. 选择'使用模拟数据'")
        print("   2. 选择不同的书籍")
        print("   3. 点击'加载角色'")
        print("   4. 观察CV下拉框显示不同的CV列表")
        print("   5. 验证CV与书籍题材的匹配度")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
