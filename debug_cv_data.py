#!/usr/bin/env python3
"""
CV数据调试脚本

检查CV数据的实际结构
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def debug_api_client():
    """调试API客户端的CV数据"""
    print("🔍 调试API客户端CV数据...")
    
    from infrastructure.api.gstudios_client import GStudiosAPIClient, APIConfig
    
    # 创建API客户端（无令牌，使用模拟数据）
    config = APIConfig("https://www.gstudios.com.cn/story_v2/api", None)
    client = GStudiosAPIClient(config)
    
    # 获取CV数据
    success, cvs = client.get_cvs("1", "human")
    
    if success:
        print(f"✅ 成功获取 {len(cvs)} 个CV")
        print("\n📋 CV数据结构详情:")
        for i, cv in enumerate(cvs):
            print(f"\nCV {i+1}:")
            print(f"  完整数据: {cv}")
            print(f"  类型: {type(cv)}")
            print(f"  键列表: {list(cv.keys()) if isinstance(cv, dict) else 'Not a dict'}")
            
            if isinstance(cv, dict):
                print(f"  id: {cv.get('id', 'MISSING')}")
                print(f"  name: {cv.get('name', 'MISSING')}")
                print(f"  gender: {cv.get('gender', 'MISSING')}")
                print(f"  age: {cv.get('age', 'MISSING')}")
    else:
        print("❌ 获取CV数据失败")

def debug_api_service():
    """调试API服务的CV数据"""
    print("\n🔍 调试API服务CV数据...")
    
    from app.container import container
    from infrastructure.api.gstudios_api_service import GStudiosAPIService
    
    # 获取API服务
    api_service = container.get(GStudiosAPIService)
    
    # 获取CV数据
    success, cvs = api_service.get_cvs("1", "human")
    
    if success:
        print(f"✅ 服务调用成功，获取 {len(cvs)} 个CV")
        print("\n📋 服务层CV数据:")
        for i, cv in enumerate(cvs):
            print(f"\nCV {i+1}:")
            print(f"  完整数据: {cv}")
            print(f"  类型: {type(cv)}")
            if isinstance(cv, dict):
                print(f"  id: {cv.get('id', 'MISSING')}")
                print(f"  name: {cv.get('name', 'MISSING')}")
    else:
        print("❌ 服务调用失败")

def test_cv_display_logic():
    """测试CV显示逻辑"""
    print("\n🔍 测试CV显示逻辑...")
    
    # 模拟不同的CV数据结构
    test_cvs = [
        {"id": "cv_001", "name": "张小雨"},  # 正常情况
        {"id": "cv_002"},  # 只有ID
        {"name": "李明轩"},  # 只有名字
        {},  # 空数据
        {"other_field": "value"}  # 其他字段
    ]
    
    print("📋 CV显示逻辑测试:")
    for i, cv in enumerate(test_cvs):
        cv_name = cv.get('name', cv.get('id', '未知CV'))
        print(f"  CV {i+1}: {cv} → 显示为: '{cv_name}'")

def main():
    """主函数"""
    print("🧪 CV数据调试")
    print("=" * 40)
    
    debug_api_client()
    debug_api_service()
    test_cv_display_logic()

if __name__ == "__main__":
    main()
