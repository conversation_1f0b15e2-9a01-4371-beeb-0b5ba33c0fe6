"""配置管理模块

此模块负责应用配置的读取、保存和管理。
"""

import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class AppConfig:
    """应用配置数据类"""
    api_token: Optional[str] = None
    api_base_url: str = "https://www.gstudios.com.cn/story_v2/api"
    remember_token: bool = True
    last_selected_book: Optional[str] = None
    window_width: int = 1200
    window_height: int = 800
    theme: str = "default"
    debug_enabled: bool = True
    log_level: str = "INFO"

    def to_dict(self) -> Dict[str, Any]:
        """转换为分层字典结构"""
        return {
            "API": {
                "base_url": self.api_base_url,
                "default_token": self.api_token
            },
            "GUI": {
                "window_width": self.window_width,
                "window_height": self.window_height,
                "theme": self.theme,
                "last_selected_book": self.last_selected_book,
                "remember_token": self.remember_token
            },
            "Debug": {
                "enabled": self.debug_enabled,
                "log_level": self.log_level
            }
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppConfig':
        """从分层字典创建配置对象"""
        api_section = data.get('API', {})
        gui_section = data.get('GUI', {})
        debug_section = data.get('Debug', {})

        return cls(
            api_token=api_section.get('default_token'),
            api_base_url=api_section.get('base_url', "https://www.gstudios.com.cn/story_v2/api"),
            remember_token=gui_section.get('remember_token', True),
            last_selected_book=gui_section.get('last_selected_book'),
            window_width=gui_section.get('window_width', 1200),
            window_height=gui_section.get('window_height', 800),
            theme=gui_section.get('theme', "default"),
            debug_enabled=debug_section.get('enabled', True),
            log_level=debug_section.get('log_level', "INFO")
        )


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器

        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 使用项目根目录下的config文件夹
            # 获取当前文件的父目录的父目录（项目根目录）
            project_root = Path(__file__).parent.parent
            self.config_dir = project_root / 'config'
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / 'config.json'
        
        # 当前配置
        self._config: Optional[AppConfig] = None
    
    def load_config(self) -> AppConfig:
        """加载配置

        Returns:
            应用配置对象
        """
        if self._config is not None:
            return self._config

        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self._config = AppConfig.from_dict(data)
                print(f"✅ 配置文件加载成功: {self.config_file}")

                # 显示加载的令牌信息（不显示完整令牌）
                if self._config.api_token:
                    token_preview = self._config.api_token[:8] + "..." if len(self._config.api_token) > 8 else self._config.api_token
                    print(f"🔑 发现保存的令牌: {token_preview}")
                else:
                    print(f"🔑 未发现保存的令牌")
            else:
                print(f"📝 配置文件不存在，使用默认配置: {self.config_file}")
                self._config = AppConfig()
        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
            self._config = AppConfig()

        return self._config
    
    def save_config(self, config: Optional[AppConfig] = None) -> bool:
        """保存配置
        
        Args:
            config: 要保存的配置对象，如果为None则保存当前配置
            
        Returns:
            保存是否成功
        """
        if config is None:
            config = self._config
        
        if config is None:
            print("⚠️ 没有配置可保存")
            return False
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            
            self._config = config
            print(f"✅ 配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 配置文件保存失败: {e}")
            return False
    
    def get_api_token(self) -> Optional[str]:
        """获取API令牌
        
        Returns:
            API令牌，如果不存在或不记住令牌则返回None
        """
        config = self.load_config()
        if config.remember_token:
            return config.api_token
        return None
    
    def set_api_token(self, token: Optional[str], remember: bool = True) -> bool:
        """设置API令牌
        
        Args:
            token: API令牌
            remember: 是否记住令牌
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.api_token = token if remember else None
        config.remember_token = remember
        return self.save_config(config)
    
    def clear_api_token(self) -> bool:
        """清除API令牌
        
        Returns:
            清除是否成功
        """
        config = self.load_config()
        config.api_token = None
        return self.save_config(config)
    
    def get_api_base_url(self) -> str:
        """获取API基础URL
        
        Returns:
            API基础URL
        """
        config = self.load_config()
        return config.api_base_url
    
    def set_api_base_url(self, url: str) -> bool:
        """设置API基础URL
        
        Args:
            url: API基础URL
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.api_base_url = url
        return self.save_config(config)
    
    def get_last_selected_book(self) -> Optional[str]:
        """获取上次选择的书籍ID
        
        Returns:
            书籍ID
        """
        config = self.load_config()
        return config.last_selected_book
    
    def set_last_selected_book(self, book_id: Optional[str]) -> bool:
        """设置上次选择的书籍ID
        
        Args:
            book_id: 书籍ID
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.last_selected_book = book_id
        return self.save_config(config)
    
    def get_window_size(self) -> tuple[int, int]:
        """获取窗口大小

        Returns:
            窗口大小元组 (width, height)
        """
        config = self.load_config()
        return (config.window_width, config.window_height)

    def set_window_size(self, width: int, height: int) -> bool:
        """设置窗口大小

        Args:
            width: 窗口宽度
            height: 窗口高度

        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.window_width = width
        config.window_height = height
        return self.save_config(config)

    def get_theme(self) -> str:
        """获取主题

        Returns:
            主题名称
        """
        config = self.load_config()
        return config.theme

    def set_theme(self, theme: str) -> bool:
        """设置主题

        Args:
            theme: 主题名称

        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.theme = theme
        return self.save_config(config)
    
    def reset_config(self) -> bool:
        """重置配置为默认值
        
        Returns:
            重置是否成功
        """
        self._config = AppConfig()
        return self.save_config()
    
    def get_config_file_path(self) -> str:
        """获取配置文件路径
        
        Returns:
            配置文件的完整路径
        """
        return str(self.config_file)


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例
    
    Returns:
        配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


# 便捷函数
def load_config() -> AppConfig:
    """加载配置（便捷函数）"""
    return get_config_manager().load_config()


def save_config(config: AppConfig) -> bool:
    """保存配置（便捷函数）"""
    return get_config_manager().save_config(config)


def get_api_token() -> Optional[str]:
    """获取API令牌（便捷函数）"""
    return get_config_manager().get_api_token()


def set_api_token(token: Optional[str], remember: bool = True) -> bool:
    """设置API令牌（便捷函数）"""
    return get_config_manager().set_api_token(token, remember)
