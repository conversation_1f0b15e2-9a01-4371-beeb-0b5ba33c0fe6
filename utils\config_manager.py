"""配置管理模块

此模块负责应用配置的读取、保存和管理。
"""

import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class AppConfig:
    """应用配置数据类"""
    api_token: Optional[str] = None
    api_base_url: str = "https://www.gstudios.com.cn/story_v2/api"
    remember_token: bool = True
    last_selected_book: Optional[str] = None
    window_geometry: Optional[Dict[str, int]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'api_token': self.api_token,
            'api_base_url': self.api_base_url,
            'remember_token': self.remember_token,
            'last_selected_book': self.last_selected_book,
            'window_geometry': self.window_geometry
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppConfig':
        """从字典创建配置对象"""
        return cls(
            api_token=data.get('api_token'),
            api_base_url=data.get('api_base_url', "https://www.gstudios.com.cn/story_v2/api"),
            remember_token=data.get('remember_token', True),
            last_selected_book=data.get('last_selected_book'),
            window_geometry=data.get('window_geometry')
        )


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为用户主目录下的.cv_assignment_tool
        """
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 使用用户主目录下的隐藏文件夹
            self.config_dir = Path.home() / '.cv_assignment_tool'
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / 'config.json'
        
        # 当前配置
        self._config: Optional[AppConfig] = None
    
    def load_config(self) -> AppConfig:
        """加载配置
        
        Returns:
            应用配置对象
        """
        if self._config is not None:
            return self._config
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self._config = AppConfig.from_dict(data)
                print(f"✅ 配置文件加载成功: {self.config_file}")
            else:
                print(f"📝 配置文件不存在，使用默认配置: {self.config_file}")
                self._config = AppConfig()
        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
            self._config = AppConfig()
        
        return self._config
    
    def save_config(self, config: Optional[AppConfig] = None) -> bool:
        """保存配置
        
        Args:
            config: 要保存的配置对象，如果为None则保存当前配置
            
        Returns:
            保存是否成功
        """
        if config is None:
            config = self._config
        
        if config is None:
            print("⚠️ 没有配置可保存")
            return False
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            
            self._config = config
            print(f"✅ 配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 配置文件保存失败: {e}")
            return False
    
    def get_api_token(self) -> Optional[str]:
        """获取API令牌
        
        Returns:
            API令牌，如果不存在或不记住令牌则返回None
        """
        config = self.load_config()
        if config.remember_token:
            return config.api_token
        return None
    
    def set_api_token(self, token: Optional[str], remember: bool = True) -> bool:
        """设置API令牌
        
        Args:
            token: API令牌
            remember: 是否记住令牌
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.api_token = token if remember else None
        config.remember_token = remember
        return self.save_config(config)
    
    def clear_api_token(self) -> bool:
        """清除API令牌
        
        Returns:
            清除是否成功
        """
        config = self.load_config()
        config.api_token = None
        return self.save_config(config)
    
    def get_api_base_url(self) -> str:
        """获取API基础URL
        
        Returns:
            API基础URL
        """
        config = self.load_config()
        return config.api_base_url
    
    def set_api_base_url(self, url: str) -> bool:
        """设置API基础URL
        
        Args:
            url: API基础URL
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.api_base_url = url
        return self.save_config(config)
    
    def get_last_selected_book(self) -> Optional[str]:
        """获取上次选择的书籍ID
        
        Returns:
            书籍ID
        """
        config = self.load_config()
        return config.last_selected_book
    
    def set_last_selected_book(self, book_id: Optional[str]) -> bool:
        """设置上次选择的书籍ID
        
        Args:
            book_id: 书籍ID
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.last_selected_book = book_id
        return self.save_config(config)
    
    def get_window_geometry(self) -> Optional[Dict[str, int]]:
        """获取窗口几何信息
        
        Returns:
            窗口几何信息字典
        """
        config = self.load_config()
        return config.window_geometry
    
    def set_window_geometry(self, geometry: Dict[str, int]) -> bool:
        """设置窗口几何信息
        
        Args:
            geometry: 窗口几何信息字典
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config.window_geometry = geometry
        return self.save_config(config)
    
    def reset_config(self) -> bool:
        """重置配置为默认值
        
        Returns:
            重置是否成功
        """
        self._config = AppConfig()
        return self.save_config()
    
    def get_config_file_path(self) -> str:
        """获取配置文件路径
        
        Returns:
            配置文件的完整路径
        """
        return str(self.config_file)


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例
    
    Returns:
        配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


# 便捷函数
def load_config() -> AppConfig:
    """加载配置（便捷函数）"""
    return get_config_manager().load_config()


def save_config(config: AppConfig) -> bool:
    """保存配置（便捷函数）"""
    return get_config_manager().save_config(config)


def get_api_token() -> Optional[str]:
    """获取API令牌（便捷函数）"""
    return get_config_manager().get_api_token()


def set_api_token(token: Optional[str], remember: bool = True) -> bool:
    """设置API令牌（便捷函数）"""
    return get_config_manager().set_api_token(token, remember)
