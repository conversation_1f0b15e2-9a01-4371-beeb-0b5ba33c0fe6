# CV分配工具启动指南

## 🎯 问题解决

### 原始问题
运行 `python app/main.py` 时出现错误：
```
ModuleNotFoundError: No module named 'app'
ImportError: attempted relative import beyond top-level package
```

### 解决方案
1. **修复了相对导入问题** - 将所有三级相对导入（`from ...module import`）改为绝对导入
2. **修复了Python路径问题** - 确保项目根目录在Python路径中
3. **简化了依赖注入容器** - 暂时禁用了未实现的服务，专注于核心功能

## 🚀 启动方式

### 方式1：启动GUI界面（推荐）
```bash
python run_gui.py
```

### 方式2：使用主启动脚本
```bash
python run.py
```

### 方式3：直接运行主程序
```bash
python app/main.py
```

### 方式4：模块方式运行
```bash
python -m app.main
```

## ✅ 成功启动标志

### GUI模式启动成功
- 看到登录对话框弹出
- 可以选择输入API令牌或使用模拟数据
- 登录成功后看到主界面窗口
- 窗口标题显示连接状态 (已连接/模拟数据)
- 界面包含菜单栏、控制面板、角色列表和CV分配面板

### 控制台模式启动成功
看到以下输出表示启动成功：
```
🚀 启动CV分配工具...
📋 独立分离架构版本
==================================================
✅ 依赖注入容器初始化成功
🎯 系统已就绪，可以开始使用
```

## 🔧 已修复的文件

### 导入修复
- `infrastructure/repositories/gstudios_character_repository.py`
- `application/use_cases/assign_cv_use_case.py`
- `application/use_cases/get_characters_use_case.py`
- `domain/entities/character.py`
- `domain/entities/assignment.py`
- `domain/entities/cv.py`
- `domain/services/cv_matcher.py`
- `domain/services/cv_matcher_adapter.py`
- `domain/repositories/character_repository.py`
- `domain/repositories/assignment_repository.py`

### 容器配置
- `app/container.py` - 简化了服务配置，暂时禁用未实现的服务
- `application/character_application_service.py` - 支持可选的分配CV功能

## 🖥️ GUI功能说明

### 界面组成
1. **控制面板** - 选择书籍和加载角色
2. **角色列表** - 显示所有角色及其CV分配状态
3. **CV分配面板** - 选择角色并分配CV

### 使用步骤
1. 启动GUI：`python run_gui.py`
2. 在登录对话框中选择认证方式：
   - 输入API令牌连接真实数据
   - 或点击"使用模拟数据"进行测试
3. 登录成功后等待书籍列表自动加载完成
4. 在控制面板的下拉框中选择要操作的书籍
5. 点击"加载角色"按钮获取该书籍的角色列表
6. 在角色列表中选择要分配CV的角色
7. 在CV分配面板中选择CV并点击"分配CV"

### 注意事项
- **登录认证**: 首次启动会显示登录对话框
- **模拟数据**: 无需API令牌即可测试所有界面功能
- **数据加载**: 使用后台线程，避免界面卡顿
- **状态显示**: 支持角色CV分配状态的可视化显示
- **重新登录**: 可通过菜单栏随时重新认证
- **当前限制**: CV分配功能显示提示信息（后端逻辑开发中）

### API配置
如需连接真实的GStudios API，请在 `config.json` 文件中设置：
```json
{
  "API": {
    "base_url": "https://www.gstudios.com.cn/story_v2/api",
    "default_token": "your_api_token_here"
  }
}
```

## 📋 当前功能状态

### ✅ 已实现
- 基本的依赖注入容器
- 角色获取功能（基础架构）
- 配置管理
- 基本的领域模型
- **PyQt5图形界面**
- **书籍列表自动加载**
- **角色列表显示**
- **CV分配界面**
- **模拟数据支持（无需API令牌即可测试）**

### ⏳ 待实现
- CV仓储实现
- 简名仓储实现
- CV分配功能（后端逻辑）
- API服务具体实现

## 🔄 下一步开发

1. **实现CV仓储**
   ```python
   # 需要在 infrastructure/repositories/ 中创建CV仓储实现
   ```

2. **实现简名仓储**
   ```python
   # 需要在 infrastructure/repositories/ 中创建简名仓储实现
   ```

3. **完善API服务**
   ```python
   # 完善 infrastructure/api/gstudios_api_service.py
   ```

4. **启用完整功能**
   ```python
   # 在 app/container.py 中启用所有服务
   ```

## 🧪 测试

### 基本功能测试
```bash
python test_main.py
```

### 单元测试（待实现）
```bash
pytest tests/
```

## 📝 注意事项

1. **Python环境** - 确保使用Python 3.8+
2. **依赖安装** - 运行前确保安装了所有依赖：`pip install -r requirements.txt`
3. **工作目录** - 确保在项目根目录下运行命令
4. **配置文件** - 某些功能可能需要配置文件，请参考相关文档

---
*最后更新: 2025-06-18*
*状态: 基本启动功能已修复 ✅*
