"""JSON配置仓储实现模块

此模块实现了基于JSON文件的配置仓储。
"""

import json
import os
from typing import Any, Dict
from .config_repository import ConfigRepository


class JsonConfigRepository(ConfigRepository):
    """JSON配置仓储实现"""
    
    def __init__(self, config_file: str = None):
        """初始化JSON配置仓储

        Args:
            config_file: 配置文件路径，默认为config/config.json
        """
        if config_file is None:
            # 使用与新配置管理器相同的路径
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent
            config_dir = project_root / 'config'
            config_dir.mkdir(exist_ok=True)
            self._config_file = str(config_dir / 'config.json')
        else:
            self._config_file = config_file
        self._config_data = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        if os.path.exists(self._config_file):
            try:
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self._config_data = {}
        else:
            self._config_data = {}
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._config_data.get(section, {}).get(key, default)
    
    def set(self, section: str, key: str, value: Any) -> None:
        """设置配置值"""
        if section not in self._config_data:
            self._config_data[section] = {}
        self._config_data[section][key] = value
    
    def save(self) -> None:
        """保存配置到文件"""
        try:
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def reload(self) -> None:
        """重新加载配置"""
        self._load_config()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置段"""
        return self._config_data.get(section, {})
