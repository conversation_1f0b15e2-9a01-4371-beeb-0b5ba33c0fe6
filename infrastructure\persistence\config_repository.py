"""配置仓储接口模块

此模块定义了配置仓储的接口，用于管理应用配置的持久化操作。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional


class ConfigRepository(ABC):
    """配置仓储接口"""
    
    @abstractmethod
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        pass
    
    @abstractmethod
    def set(self, section: str, key: str, value: Any) -> None:
        """设置配置值"""
        pass
    
    @abstractmethod
    def save(self) -> None:
        """保存配置到持久化存储"""
        pass
    
    @abstractmethod
    def reload(self) -> None:
        """从持久化存储重新加载配置"""
        pass
    
    @abstractmethod
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置段"""
        pass
