"""书籍DTO模块

此模块定义了书籍相关的数据传输对象。
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class BookDTO:
    """书籍数据传输对象"""
    
    id: str
    name: str
    description: Optional[str] = None
    finished: bool = False
    author: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def __str__(self) -> str:
        """字符串表示"""
        status = "已完成" if self.finished else "进行中"
        return f"{self.name} ({status})"


@dataclass
class GetBooksRequest:
    """获取书籍列表请求"""
    
    finished: str = "all"  # "all", "finished", "unfinished"
    page_no: int = 1
    page_size: int = 50


@dataclass
class GetBooksResponse:
    """获取书籍列表响应"""
    
    success: bool
    books: List[BookDTO]
    total_count: int = 0
    error_message: Optional[str] = None
    
    @classmethod
    def success_response(cls, books: List[BookDTO], total_count: int = None) -> 'GetBooksResponse':
        """创建成功响应"""
        if total_count is None:
            total_count = len(books)
        return cls(success=True, books=books, total_count=total_count)
    
    @classmethod
    def error_response(cls, error_message: str) -> 'GetBooksResponse':
        """创建错误响应"""
        return cls(success=False, books=[], error_message=error_message)
