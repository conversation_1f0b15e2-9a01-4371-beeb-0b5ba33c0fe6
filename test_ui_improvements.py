#!/usr/bin/env python3
"""
UI改进功能测试脚本

测试角色表格和书籍分类的改进
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def test_book_filtering():
    """测试书籍分类过滤功能"""
    try:
        print("🔍 测试书籍分类过滤功能...")
        from app.container import container
        from application.book_application_service import BookApplicationService
        
        # 获取书籍服务
        book_service = container.get(BookApplicationService)
        print("✅ 书籍服务获取成功")
        
        # 获取所有书籍
        all_books_response = book_service.get_books("all")
        if all_books_response.success:
            all_books = all_books_response.books
            print(f"📚 总共 {len(all_books)} 本书籍:")
            for book in all_books:
                status = "已完成" if book.finished else "进行中"
                print(f"  - {book.name} ({status})")
            
            # 测试分类过滤
            finished_books = [book for book in all_books if book.finished]
            unfinished_books = [book for book in all_books if not book.finished]
            
            print(f"\n📊 分类统计:")
            print(f"  - 全部书籍: {len(all_books)} 本")
            print(f"  - 进行中: {len(unfinished_books)} 本")
            print(f"  - 已完成: {len(finished_books)} 本")
            
            return True
        else:
            print(f"❌ 获取书籍失败: {all_books_response.error_message}")
            return False
        
    except Exception as e:
        print(f"❌ 书籍分类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_display():
    """测试角色显示改进"""
    try:
        print("\n🔍 测试角色显示改进...")
        from app.container import container
        from application.character_application_service import CharacterApplicationService
        
        # 获取角色服务
        character_service = container.get(CharacterApplicationService)
        print("✅ 角色服务获取成功")
        
        # 获取角色列表
        response = character_service.get_characters("1")
        if response.success:
            print(f"👥 获取到 {len(response.characters)} 个角色:")
            print("\n角色表格预览 (模拟GUI显示):")
            print("=" * 60)
            print(f"{'角色名称':<15} {'当前CV':<15} {'状态':<10}")
            print("-" * 60)
            
            for character in response.characters:
                # 模拟GUI的显示逻辑
                if character.cv_name:
                    cv_text = character.cv_name
                elif character.cv_id:
                    cv_text = character.cv_id
                else:
                    cv_text = "未分配"
                
                status = "已分配" if character.cv_id else "未分配"
                
                print(f"{character.name:<15} {cv_text:<15} {status:<10}")
            
            print("=" * 60)
            print("\n💡 改进说明:")
            print("  - 移除了角色ID列，ID现在作为工具提示显示")
            print("  - 角色名称列宽度可调整，初始宽度150px")
            print("  - CV列优先显示名字，自适应宽度")
            print("  - 状态列适应内容宽度")
            
            return True
        else:
            print(f"❌ 获取角色失败: {response.error_message}")
            return False
        
    except Exception as e:
        print(f"❌ 角色显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_layout():
    """测试GUI布局改进"""
    print("\n🔍 测试GUI布局改进...")
    
    print("📋 控制面板改进:")
    print("  - 添加了书籍分类下拉框")
    print("  - 分类选项: 全部书籍、进行中、已完成")
    print("  - 分类变化时自动过滤书籍列表")
    
    print("\n📊 角色表格改进:")
    print("  - 列数从4列减少到3列")
    print("  - 移除角色ID列，ID作为工具提示显示")
    print("  - 角色名称列可调整宽度，初始150px")
    print("  - CV列自适应内容，优先显示名字")
    print("  - 状态列适应内容宽度")
    
    print("\n🎨 用户体验改进:")
    print("  - 更简洁的表格布局")
    print("  - 更合理的列宽分配")
    print("  - 书籍分类便于快速筛选")
    print("  - 工具提示提供详细信息")
    
    return True

def main():
    """主测试函数"""
    print("🧪 CV分配工具 - UI改进功能测试")
    print("=" * 50)
    
    # 测试书籍分类过滤
    book_success = test_book_filtering()
    
    # 测试角色显示改进
    character_success = test_character_display()
    
    # 测试GUI布局改进
    layout_success = test_gui_layout()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"  - 书籍分类过滤: {'✅ 通过' if book_success else '❌ 失败'}")
    print(f"  - 角色显示改进: {'✅ 通过' if character_success else '❌ 失败'}")
    print(f"  - GUI布局改进: {'✅ 通过' if layout_success else '❌ 失败'}")
    
    if all([book_success, character_success, layout_success]):
        print("\n🎉 所有测试通过！UI改进功能正常。")
        print("\n🚀 启动GUI查看效果:")
        print("   python run_gui.py")
        print("\n💡 预期改进:")
        print("   1. 角色表格更简洁，无角色ID列")
        print("   2. 角色名称列宽度合适，可调整")
        print("   3. 书籍分类选择，便于筛选")
        print("   4. CV名字优先显示，用户体验更好")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
