"""CV实体模块

此模块定义了CV(配音演员)实体，包含CV的基本信息和业务行为。
"""

from dataclasses import dataclass
from typing import Optional, List
from domain.value_objects.cv_id import CVID
from domain.exceptions.cv_exceptions import InvalidCVError


@dataclass
class CV:
    """CV(配音演员)实体
    
    CV是系统的核心实体之一，代表可以分配给角色的配音演员。
    
    Attributes:
        id: CV唯一标识符
        name: CV名称
        is_available: 是否可用
        book_id: 所属书籍标识符
        type: CV类型 ('human' 或 'robot')
        description: CV描述，可选
        nicknames: CV的简名列表
    """
    
    id: CVID
    name: str
    book_id: str
    is_available: bool = True
    type: str = 'human'  # 'human' 或 'robot'
    description: Optional[str] = None
    nicknames: Optional[List[str]] = None
    
    def __post_init__(self):
        """初始化后验证"""
        self._validate()
        if self.nicknames is None:
            object.__setattr__(self, 'nicknames', [])
    
    def _validate(self) -> None:
        """验证CV数据的有效性"""
        if not self.name or not self.name.strip():
            raise InvalidCVError("CV名称不能为空")
        
        if not self.book_id or not self.book_id.strip():
            raise InvalidCVError("书籍ID不能为空")
        
        if self.type not in ('human', 'robot'):
            raise InvalidCVError("CV类型必须是 'human' 或 'robot'")
        
        # 规范化名称（去除首尾空格）
        object.__setattr__(self, 'name', self.name.strip())
    
    def add_nickname(self, nickname: str) -> None:
        """添加简名
        
        Args:
            nickname: 要添加的简名
            
        Raises:
            InvalidCVError: 当简名无效时抛出
        """
        if not nickname or not nickname.strip():
            raise InvalidCVError("简名不能为空")
        
        nickname = nickname.strip()
        if nickname not in self.nicknames:
            self.nicknames.append(nickname)
    
    def remove_nickname(self, nickname: str) -> None:
        """移除简名
        
        Args:
            nickname: 要移除的简名
        """
        if nickname in self.nicknames:
            self.nicknames.remove(nickname)
    
    def has_nickname(self, nickname: str) -> bool:
        """检查是否有指定的简名
        
        Args:
            nickname: 要检查的简名
            
        Returns:
            bool: 是否有该简名
        """
        return nickname in self.nicknames
    
    def matches_name_or_nickname(self, name: str) -> bool:
        """检查名称是否匹配CV名称或简名
        
        Args:
            name: 要匹配的名称
            
        Returns:
            bool: 是否匹配
        """
        if not name:
            return False
        
        name = name.strip()
        
        # 检查是否匹配CV名称
        if self.name == name:
            return True
        
        # 检查是否匹配任何简名
        return name in self.nicknames
    
    def set_availability(self, available: bool) -> None:
        """设置可用性
        
        Args:
            available: 是否可用
        """
        object.__setattr__(self, 'is_available', available)
    
    def is_human(self) -> bool:
        """检查是否是人工CV"""
        return self.type == 'human'
    
    def is_robot(self) -> bool:
        """检查是否是AI CV"""
        return self.type == 'robot'
    
    def get_display_name(self) -> str:
        """获取显示名称
        
        Returns:
            str: 用于显示的CV名称
        """
        type_suffix = " (AI)" if self.is_robot() else ""
        availability_suffix = "" if self.is_available else " (不可用)"
        return f"{self.name}{type_suffix}{availability_suffix}"
    
    def get_all_names(self) -> List[str]:
        """获取所有名称（包括主名称和简名）
        
        Returns:
            List[str]: 所有名称的列表
        """
        names = [self.name]
        names.extend(self.nicknames)
        return names
    
    def __str__(self) -> str:
        """字符串表示"""
        status = "可用" if self.is_available else "不可用"
        type_info = "AI" if self.is_robot() else "人工"
        return f"CV[{self.name}] ({type_info}, {status})"
    
    def __repr__(self) -> str:
        """开发者友好的字符串表示"""
        return (f"CV(id={self.id!r}, name={self.name!r}, "
                f"book_id={self.book_id!r}, type={self.type!r}, "
                f"is_available={self.is_available})")
    
    def __eq__(self, other) -> bool:
        """相等性比较 - 基于ID"""
        if not isinstance(other, CV):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """哈希值 - 基于ID"""
        return hash(self.id)
